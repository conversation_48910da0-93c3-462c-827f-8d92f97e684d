/* eslint-disable */
import { tokenize, createDefaultGrammar } from "@livekit/components-core";
import { Avatar, Modal, Popover } from "antd";
import * as React from "react";
import { useState } from "react";
import { Document, Page } from "react-pdf";

import {
  generateAvatar,
  getParticipantColor,
} from "../../utils/helper";
import { ReactComponent as ReplyIco } from "./Assets/replyIco.svg";
import { ReactComponent as ImageIcon } from "./Assets/imageIcon.svg";
import { ReactComponent as PDFIcon } from "./Assets/pdfIcon.svg";
import { ReactComponent as EditMessageIco } from "./Assets/editMessage.svg";
import { ReactComponent as DeleteMessageIco } from "./Assets/deleteMessage.svg";
import { ReactComponent as PinMessageIco } from "./Assets/pinMessage.svg";
import { ReactComponent as CopyLinkIco } from "./Assets/copyLink.svg";
import EmojiPicker from "emoji-picker-react";
import { DataReceivedEvent } from "../../utils/constants";
import { BsThreeDots } from "react-icons/bs";
import { useParticipantContext } from "../../context/ParticipantContext";
import AttachmentPreview from "./AttachmentPreview";
// import { DownScaleImage } from "./DownScaleImage";

/** @public */
const ChatsEntry = React.forwardRef(
  (
    {
      entry,
      hideName = false,
      hideTimestamp = false,
      messageFormatter,
      uploadedfile,
      uploadedFileName,
      showprivatechatdrawer,
      setReplyMessage,
      replyMessage,
      chatMessages,
      setChatMessages,
      scrollToRepliedChat,
      localparticipant,
      className,
      mode,
      onEditMessage,
      onDeleteMessage,
      onPinMessage,
      scrollToBottom,
      ...props
    },
    ref
  ) => {
    const hasBeenEdited = !!entry.editTimestamp;
    const time = new Date(entry.timestamp);
    const locale = navigator ? navigator.language : "en-US";

    const { participantColors } = useParticipantContext();

    const [showAttatchmentPreview, setShowAttatchmentPreview] = useState(false);

    const handlePreview = () => {
      setShowAttatchmentPreview(true);
    };

    const formattedMessage = React.useMemo(() => {
      return messageFormatter
        ? messageFormatter(
            entry.message.replace(/\n/g, "<br />"),
            handlePreview,
            uploadedFileName
          )
        : entry.message.replace(/\n/g, "<br />");
    }, [entry.message, messageFormatter]);

    const handleReplyToMessage = (name, message, id, identity) => {
      setReplyMessage({ name, message, id, identity });
    };



    const [reactions, setReactions] = useState(entry?.reactions || []);
    const [showReactionsModal, setShowReactionsModal] = useState(false);



    return (
      <li
        ref={ref}
        className={`lk-chat-entry
          ${entry?.from?.isLocal ? "lk-local" : ""} 
          ${entry?.isTranscription ? "lk-transcription" : ""}
          ${className}`}
        title={time.toLocaleTimeString(locale, { timeStyle: "full" })}
        data-lk-message-origin={entry.from?.isLocal ? "local" : "remote"}
        {...props}
      >
        <AttachmentPreview
          isOpen={showAttatchmentPreview}
          onClose={() => setShowAttatchmentPreview(false)}
          message={entry.message}
          canDownloadChatAttachment={props.canDownloadChatAttachment}
        />
        {(!hideTimestamp || !hideName || hasBeenEdited) && (
          <span className="lk-meta-data">
            <div className="lk-meta-data-name">
              {entry.from?.isLocal || showprivatechatdrawer
                ? null
                : !hideName && (
                    <Avatar
                      style={{
                        backgroundColor: getParticipantColor(
                          participantColors,
                          entry.from?.identity
                        ),
                      }}
                      size="medium"
                    >
                      {generateAvatar(
                        // Use "Agent" for avatar generation if it's an agent with empty name
                        !entry.from?.name &&
                          (entry.from?.identity?.startsWith("agent-") ||
                            entry.from?._attributes?.["lk.agent.state"])
                          ? "Agent"
                          : entry.from?.name
                      )}
                    </Avatar>
                  )}
              {!hideName && (
                <strong className="lk-participant-name">
                  {entry.from?.isLocal
                    ? "You"
                    : // Check if it's an agent participant with empty name
                    !entry.from?.name &&
                      (entry.from?.identity?.startsWith("agent-") ||
                        entry.from?._attributes?.["lk.agent.state"])
                    ? "Agent"
                    : entry.from?.name}
                </strong>
              )}
            </div>
          </span>
        )}
        <div
          className={`${
            entry?.from?.isLocal
              ? "lk-message-outer"
              : "lk-message-outer-remote"
          }`}
        >
          {entry.deleted ? (
            <span>
              <span className="lk-message-body lk-message-body-remote lk-message-body-deleted">
                Message Deleted
              </span>
            </span>
          ) : (
            <Popover
              content={
                <div className="lk-message-body-emoji-reaction-popover">
                  {mode === "public" && (
                    // || (mode === "private" && !entry.from?.isLocal)
                    <EmojiPicker
                      className="lk-message-body-emoji-reaction-picker"
                      onEmojiClick={async (emoji, event) => {
                        // Prevent default behavior and stop propagation to avoid scroll
                        event.preventDefault();
                        event.stopPropagation();

                        const react = {
                          emoji: emoji.unified,
                          reactor: localparticipant.identity,
                          name: localparticipant.name,
                        };

                        // Check if user already has this exact reaction
                        const hasSameReaction = entry.reactions?.some(
                          (r) =>
                            r.reactor === localparticipant.identity &&
                            r.emoji === emoji.unified
                        );

                        const encoder = new TextEncoder();
                        const encodedMessage = encoder.encode(
                          JSON.stringify({
                            action: DataReceivedEvent.ADD_REACTION,
                            mode: mode,
                            messageId: entry.id,
                            reaction: react,
                            removeReaction: hasSameReaction, // Add flag to indicate if reaction should be removed
                          })
                        );
                        await localparticipant.publishData(encodedMessage, {
                          reliable: true,
                          ...(mode === "private" &&
                          props.privateChatRecipientIdentity
                            ? {
                                destinationIdentities: [
                                  props.privateChatRecipientIdentity,
                                ],
                              }
                            : {}),
                        });
                        setChatMessages((prevMessages) => {
                          const updatedMessages = prevMessages.map((msg) =>
                            msg.id === entry.id
                              ? {
                                  ...msg,
                                  reactions: hasSameReaction
                                    ? msg.reactions.filter(
                                        (r) =>
                                          r.reactor !==
                                          localparticipant.identity
                                      )
                                    : msg.reactions?.some(
                                        (r) =>
                                          r.reactor ===
                                          localparticipant.identity
                                      )
                                    ? msg.reactions
                                        .filter(
                                          (r) =>
                                            r.reactor !==
                                            localparticipant.identity
                                        )
                                        .concat([react])
                                    : [...(msg.reactions || []), react],
                                }
                              : msg
                          );
                          return updatedMessages;
                        });
                      }}
                      reactionsDefaultOpen={true}
                      emojiStyle={"google"}
                      height={300}
                      width={230}
                      skinTonesDisabled={true}
                      allowExpandReactions={false}
                      // reactions={["👍", "👎", "👋", "👌"]}
                    />
                  )}
                  {mode === "public" && (
                    // || (mode === "private" && !entry.from?.isLocal)
                    <span className="lk-message-body-emoji-reaction-separator" />
                  )}
                  <ReplyIco
                    className="lk-message-body-reply-icon"
                    onClick={(e) => {
                      e.stopPropagation();
                      const displayName =
                        !entry.from?.name &&
                        (entry.from?.identity?.startsWith("agent-") ||
                          entry.from?._attributes?.["lk.agent.state"])
                          ? "Agent"
                          : entry.from?.name;
                      handleReplyToMessage(
                        displayName,
                        entry.message,
                        entry.id,
                        entry.from?.identity,
                      );
                    }}
                  />
                  <Popover
                    content={
                      <div className="lk-message-body-more-options-content">
                        {entry.from?.isLocal && (
                          <div
                            className="lk-message-body-more-options-content-item"
                            onClick={() => {
                              if (entry.from?.isLocal && onEditMessage) {
                                onEditMessage(entry);
                              }
                            }}
                          >
                            <EditMessageIco />
                            <span>Edit Message</span>
                          </div>
                        )}
                        {entry.from?.isLocal && (
                          <div
                            className="lk-message-body-more-options-content-item"
                            onClick={() => {
                              if (entry.from?.isLocal && onDeleteMessage) {
                                onDeleteMessage(entry);
                              }
                            }}
                          >
                            <DeleteMessageIco />
                            <span>Delete Message</span>
                          </div>
                        )}
                        <div
                          className="lk-message-body-more-options-content-item"
                          onClick={() => {
                            onPinMessage(entry);
                          }}
                        >
                          <PinMessageIco />
                          <span>Pin Message</span>
                        </div>
                        {entry.message.startsWith("https://") && (
                          <div
                            className="lk-message-body-more-options-content-item"
                            onClick={() => {
                              navigator.clipboard.writeText(entry.message);
                            }}
                          >
                            <CopyLinkIco />
                            <span>Copy Link</span>
                          </div>
                        )}
                      </div>
                    }
                    overlayClassName="lk-message-body-more-options"
                    trigger="click"
                    placement="bottomRight"
                    arrow={false}
                    align={{
                      offset: [8, 10],
                    }}
                  >
                    <BsThreeDots className="lk-message-body-more-options-ico" />
                  </Popover>
                </div>
              }
              arrow={false}
              // trigger="click"
              overlayClassName="lk-message-body-reply"
              placement={
                mode === "public"
                  ? entry?.from?.isLocal
                    ? "topRight"
                    : "topLeft"
                  : entry?.from?.isLocal
                  ? "left"
                  : "right"
              }
            >
              <span
                className={`lk-message-body
              ${
                entry?.from?.isLocal
                  ? "lk-message-body-local"
                  : "lk-message-body-remote"
              }
              ${
                /jpeg|jpg|png|gif/.test(entry.message)
                  ? "lk-message-body-image"
                  : ""
              }
              ${/pdf/.test(entry.message) ? "lk-message-body-pdf" : ""}
              ${entry.replyMessage ? "lk-message-body-replied-message" : ""}
                ${entry.isTranscription ? "lk-transcription-message" : ""}`}
                onClick={() => {
                  if (entry.replyMessage) {
                    scrollToRepliedChat(entry.replyMessage.id);
                  } else if (
                    typeof entry.message === "string" &&
                    entry.message.startsWith("https://")
                  ) {
                    window.open(entry.message, "_blank");
                  }
                }}
              >
                <div
                  className={`lk-chat-body-outer ${
                    /pdf/.test(entry.message) ? "lk-chat-pdf" : ""
                  }`}
                >
                  {entry.isReplied && (
                    <div className="lk-chat-body-inner">
                      <div className="lk-chat-body-inner-avtar">
                        <Avatar
                          style={{
                            backgroundColor: getParticipantColor(
                              participantColors,
                              entry.replyMessage?.identity
                            ),
                          }}
                          size={20}
                        >
                          {generateAvatar(entry.replyMessage?.name || "Agent")}
                        </Avatar>
                        <span className="lk-chat-reply-name">
                          {entry.replyMessage?.name || "Agent"}
                        </span>
                      </div>
                      {entry.isReplied &&
                        (/jpeg|png|gif|jpg/.test(entry.replyMessage.message) ? (
                          <div className="lk-chat-reply-image">
                            <div className="lk-chat-reply-image-box">
                              <ImageIcon />{" "}
                              {entry?.replyMessage?.message
                                .split(" ")
                                .slice(1)
                                .join(" ")
                                .substring(0, 20) +
                                (entry?.replyMessage?.message.length > 20
                                  ? "..."
                                  : "")}
                              {/* <div>Image</div> */}
                            </div>
                          </div>
                        ) : /pdf/.test(entry.replyMessage.message) ? (
                          <div className="lk-chat-reply-image">
                            <div className="lk-chat-reply-image-box">
                              <PDFIcon />{" "}
                              {entry?.replyMessage?.message
                                .split(" ")
                                .slice(1)
                                .join(" ")
                                .substring(0, 20) +
                                (entry?.replyMessage?.message.length > 20
                                  ? "..."
                                  : "")}
                            </div>
                          </div>
                        ) : (
                          <div className="lk-chat-reply">
                            <span className="lk-chat-reply-message">
                              {entry.replyMessage?.message.length > 50
                                ? `${entry.replyMessage?.message.substring(
                                    0,
                                    50
                                  )}...`
                                : entry.replyMessage?.message}
                            </span>
                          </div>
                        ))}
                    </div>
                  )}
                  {Array.isArray(formattedMessage) ? (
                    formattedMessage.map((item, idx) =>
                      typeof item === "string" ? (
                        <span
                          className="lk-chat-text"
                          key={idx}
                          dangerouslySetInnerHTML={{ __html: item }}
                        />
                      ) : (
                        React.cloneElement(item, { key: idx })
                      )
                    )
                  ) : typeof formattedMessage === "string" ? (
                    <span
                      className="lk-chat-text"
                      dangerouslySetInnerHTML={{ __html: formattedMessage }}
                    />
                  ) : (
                    formattedMessage
                  )}
                </div>
              </span>
              {entry?.reactions?.length > 0 && (
                <div
                  className={`${
                    entry?.from?.isLocal
                      ? "lk-message-body-emoji-reaction-local"
                      : "lk-message-body-emoji-reaction-remote"
                  }
              reacted
              ${entry?.reactions?.length > 1 ? "reacted-more" : ""}
              ${entry?.reactions?.length === 1 ? "one-reaction" : ""}`}
                >
                  {(() => {
                    // Show the localparticipant's reaction first if present, for both sender and receiver
                    const userReaction = entry?.reactions?.find(
                      (r) => r.reactor === localparticipant.identity
                    );
                    let reactionsToShow = [];
                    if (userReaction) {
                      reactionsToShow = [
                        userReaction,
                        ...entry?.reactions?.filter(
                          (r) => r.reactor !== localparticipant.identity
                        ),
                      ];
                    } else {
                      reactionsToShow = entry?.reactions || [];
                    }
                    const visibleReactions = reactionsToShow.slice(0, 3);
                    const remainingCount = reactionsToShow.length - 3;
                    return (
                      <>
                        {visibleReactions.map((reaction, idx) => (
                          <div
                            key={idx}
                            className="lk-message-body-emoji-reaction-item"
                            onClick={(e) => {
                              e.stopPropagation();
                              setReactions(entry?.reactions);
                              setShowReactionsModal(true);
                            }}
                          >
                            {String.fromCodePoint(
                              parseInt(reaction?.emoji, 16)
                            )}
                          </div>
                        ))}
                        {remainingCount > 0 && (
                          <span className="lk-message-body-emoji-reaction-separator reacted-more" />
                        )}
                        {remainingCount > 0 && (
                          <div
                            className="lk-message-body-emoji-reaction-item"
                            onClick={() => {
                              setReactions(entry?.reactions);
                              setShowReactionsModal(true);
                            }}
                          >
                            +{remainingCount}
                          </div>
                        )}
                      </>
                    );
                  })()}
                </div>
              )}
              {hasBeenEdited && (
                <span
                  className={`lk-message-body-edited-indicator ${
                    entry?.from?.isLocal
                      ? "lk-message-body-edited-indicator-local"
                      : "lk-message-body-edited-indicator-remote"
                  }`}
                >
                  Edited
                </span>
              )}
            </Popover>
          )}
        </div>
        <Modal
          open={showReactionsModal}
          onCancel={() => setShowReactionsModal(false)}
          footer={null}
          title={`${entry?.reactions?.length} Reactions`}
          className="lk-message-body-emoji-reaction-modal"
        >
          {/* <h5>{`${entry?.reactions?.length} Reactions`}</h5> */}
          <div className="lk-message-body-emoji-reaction-modal-items">
            {reactions.map((reaction, idx) => (
              <div
                key={idx}
                className="lk-message-body-emoji-reaction-modal-item"
              >
                <div className="lk-message-body-emoji-reaction-modal-item-avatar">
                  <Avatar
                    style={{
                      backgroundColor: getParticipantColor(
                        participantColors,
                        reaction?.reactor
                      ),
                    }}
                    className="lk-message-body-emoji-reaction-modal-item-avatar-img"
                    size={20}
                  >
                    {generateAvatar(reaction?.name)}
                  </Avatar>
                  <div className="lk-message-body-emoji-reaction-modal-item-name">
                    {reaction?.name}
                  </div>
                </div>
                <span className="lk-message-body-emoji-reaction-modal-item-emoji">
                  {String.fromCodePoint(parseInt(reaction?.emoji, 16))}
                </span>
              </div>
            ))}
          </div>
        </Modal>
      </li>
    );
  }
);

export { ChatsEntry };

/** @public */
export function formatChatMessageLinks(message, handlePreview) {
  return tokenize(message, createDefaultGrammar()).map((tok, i) => {
    if (typeof tok === "string") {
      return tok;
    } else {
      const content = tok.content.toString();
      // console.log("content",message.split(" ")[1]);
      const href =
        tok.type === "url"
          ? /^http(s?):\/\//.test(content)
            ? content
            : `https://${content}`
          : `mailto:${content}`;

      // Check if it's an image or PDF
      if (tok.type === "url" && /\.(jpg|jpeg|png|gif)$/i.test(content)) {
        // Extract the text after the image link from the message
        // const remainingMessage = message.split(content)[1]?.trim() || '';

        // Return an image preview with dynamic text below
        return (
          <div key={i} className="lk-chat-media-img">
            <img
              src={`${href}?w=200px&h=200px`}
              alt="Attatchment Preview"
              className="lk-chat-image-preview"
              onClick={(e) => {
                e.stopPropagation();
                handlePreview(); // Trigger modal here
              }}
            />
            {/* <DownScaleImage
              href={href}
              handlePreview={handlePreview}
            /> */}
            {/* <p className="lk-chat-text-below">{remainingMessage}</p> */}
          </div>
        );
      }
      // else if(tok.type === 'url' && !/\.(jpg|jpeg|png|gif|pdf)$/i.test(content)) {
      //   return (
      //     <a
      //       className="lk-chat-link"
      //       key={i}
      //       href={href}
      //       target="_blank"
      //       rel="noreferrer"
      //     >
      //       {content}
      //     </a>
      //   );
      // }
      else if (tok.type === "url" && /\.pdf$/i.test(content)) {
        // Extract the text after the PDF link
        // const remainingMessage = message.split(content)[1]?.trim() || '';
        const fileName = message.split("-file-").pop();
        // Return a PDF preview with dynamic text below
        return (
          <div key={i} className="lk-chat-media">
            {/* <iframe
              type="application/pdf"
              title={content}
              src={`https://docs.google.com/gview?url=${href}&embedded=true`}
              className="lk-chat-pdf-preview"
              style={{width: "12.2rem", height: "auto"}}
            /> */}
            <div className="lk-chat-media-pdf">
              <Document
                file={href}
                className="lk-chat-pdf-preview"
                loading=""
                onClick={(e) => {
                  e.stopPropagation();
                  handlePreview(); // Trigger modal here
                }}
              >
                <Page
                  pageNumber={1}
                  width={180}
                  className={"lk-chat-pdf-page"}
                  renderAnnotationLayer={false}
                  renderTextLayer={false}
                />
              </Document>
              <div className="lk-chat-media-overlay">
                <span className="lk-chat-pdf-title">
                  {fileName.length > 15
                    ? `${fileName
                        .split(".")
                        .slice(0, -1)
                        .join(".")
                        .substring(0, 12)}...${fileName.split(".").pop()}`
                    : fileName}
                </span>
                <MdFileDownload
                  onClick={(e) => {
                    e.stopPropagation();
                    window.open(href, "_blank");
                  }}
                  className="lk-chat-pdf-download"
                />
              </div>
            </div>
            {/* <p className={`${message.split(" ")[1] ? "lk-chat-media-message-show" : "lk-chat-media-message-hide"}`}>
              {message.split(" ")[1]}
            </p> */}
          </div>
        );
      }

      return (
        <a
          className="lk-chat-link"
          key={i}
          href={href}
          target="_blank"
          rel="noreferrer"
        >
          {content}
        </a>
      );
    }
  });
}
