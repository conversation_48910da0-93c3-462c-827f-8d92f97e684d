import React, { useState } from "react";
import { Modal } from "antd";
import { MdFileDownload, MdOutlineZoomIn, MdOutlineZoomOut, MdZoomInMap } from "react-icons/md";

function AttachmentPreview({
  isOpen,
  onClose,
  message,
  canDownloadChatAttachment = false
}) {
  const [scale, setScale] = useState(1);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [startPos, setStartPos] = useState({ x: 0, y: 0 });

  const zoomIn = () => setScale((prev) => Math.min(prev + 0.2, 3));
  const zoomOut = () => setScale((prev) => Math.max(prev - 0.2, 1));
  const resetZoom = () => setScale(1);

  const handleMouseDown = (e) => {
    setIsDragging(true);
    setStartPos({ x: e.clientX - position.x, y: e.clientY - position.y });
  };

  const handleMouseMove = (e) => {
    if (!isDragging) return;
    setPosition({
      x: e.clientX - startPos.x,
      y: e.clientY - startPos.y,
    });
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleDownload = () => {
    window.open(message.split(" ")[0], "_blank");
  };

  const handleClose = () => {
    setPosition({ x: 0, y: 0 });
    setScale(1);
    onClose();
  };

  const attachmentUrl = message.split(" ")[0];
  const fileName = message.split("-file-").pop();
  const isImage = /jpeg|jpg|png|gif/.test(message);
  const isPdf = /pdf/.test(message);

  return (
    <Modal
      open={isOpen}
      onCancel={handleClose}
      footer={null}
      className="lk-attatchment-preview"
      maskStyle={{
        background: "rgba(0, 0, 0, 0.7)",
        backdropFilter: "blur(4px)",
      }}
    >
      {canDownloadChatAttachment && (
        <MdFileDownload
          onClick={handleDownload}
          className="attatchment-download"
        />
      )}
      
      <span className="attatchment-name">
        {fileName}
      </span>

      {isPdf && (
        <iframe
          type="application/pdf"
          title={message}
          src={attachmentUrl}
          className="lk-chat-pdf-preview"
          style={{ width: "100%", height: "500px" }}
          allow="fullscreen"
          sandbox="allow-same-origin allow-scripts allow-popups allow-forms"
        />
      )}

      {isImage && (
        <div
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
          style={{
            cursor: isDragging ? "grabbing" : "grab",
            background: "transparent",
          }}
        >
          <img
            src={attachmentUrl}
            alt="Attachment Preview"
            style={{
              width: "100%",
              transform: `translate(${position.x}px, ${position.y}px) scale(${scale})`,
              transition: isDragging ? "none" : "transform 0.2s ease",
              userSelect: "none",
            }}
            onDoubleClick={zoomIn}
          />
          
          <MdOutlineZoomIn
            onClick={zoomIn}
            className="attatchment-zoom-in"
          />
          <MdOutlineZoomOut
            onClick={zoomOut}
            className="attatchment-zoom-out"
          />
          <MdZoomInMap
            onClick={resetZoom}
            className="attatchment-zoom-reset"
          />
        </div>
      )}
    </Modal>
  );
}

export default AttachmentPreview;
