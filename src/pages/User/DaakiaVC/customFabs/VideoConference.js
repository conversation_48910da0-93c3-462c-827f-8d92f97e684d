/* eslint-disable */
import React, { useEffect, useState, useRef, useCallback, useMemo } from "react";
import "../styles/VideoConference.scss";
import {
  isEqualTrackRef,
  isTrackReference,
  isWeb,
  isMobileBrowser,
  log,
} from "@livekit/components-core";
import {
  RoomEvent,
  Track,
  DisconnectReason,
  ConnectionState,
} from "livekit-client";
import { Button } from "antd";
import {
  CarouselLayout,
  FocusLayoutContainer,
  GridLayout,
  LayoutContextProvider,
  RoomAudioRenderer,
  usePinnedTracks,
  useTracks,
  useCreateLayoutContext,
  TrackRefContext,
  MediaDeviceMenu,
} from "@livekit/components-react";
import moment from "moment";
import { ReactComponent as DaakiaLogo } from "./icons/DaakiaLogoLight.svg";
import { ReactComponent as VideoSwitch } from "../assets/icons/videoSwitch.svg";
import {
  constants,
  DataReceivedEvent,
  DrawerState,
  PROCESS_EVENT,
  SocketChannel,
  TOPIC_NAME
} from "../utils/constants";
import { operationHandlers } from '../utils/ai-operations';
import { formatChatMessageLinks } from "../components/chats/ChatsEntry";
import useSocket from "../hooks/useSocket";
import { parseMetadata } from "../utils/helper";

import { ControlBar } from "./ControlBar";
import { ParticipantTile } from "./ParticipantTile";


import { MeetindEndedModal } from "../components/settings/MeetindEndingModal";
import { NotificationModal } from "../components/NotificationModal";
import AllowParticipant from "../components/AllowParticipant";

import recordingStart from "../assets/sounds/recording_start.mp3";
import recordingStop from "../assets/sounds/recording_stop.mp3";
import lobbyNotification from "../assets/sounds/lobbyNotification.mp3";
import { BreakoutRoomService } from "../services/BreakoutRoomServices";
import { getLocalStorage, setLocalStorage } from "../utils/helper";
import Whiteboard from "../components/whiteboard/Whiteboard";
import { ConnectionStateToast } from "./ConnectionStateToast";
import { WhiteboardService } from "../services/WhiteboardServices";
import isElectron from "is-electron";
import PipLayout from "./PipLayout";
import { SettingsMenuServices } from "../services/SettingsMenuServices";
import DrawerContainer from "./DrawerContainer";

import StatusNotification from "../components/StatusNotification/StatusNotification";
import { generateUUID } from "../utils/helper";
import { datadogLogs } from "@datadog/browser-logs";
import { useVideoConferencesContext } from "../context/VideoConferencesContext";



export function VideoConference({
  room,
  setMovingRoomToken,
  setIsMovingToRoom,
  SettingsComponent,
  isMovingToRoom,
  meetingFeatures,
  isWebinarMode,
  setIsUsingBreakoutRoom,
  isElectronApp,
  screenShareSources,
  isPipWindow,
  isSelfVideoMirrored,
  setIsSelfVideoMirrored,
  deviceIdAudio,
  setDeviceIdAudio,
  deviceIdVideo,
  setDeviceIdVideo,
  brightness,
  onBrightnessChange,
  outputVolume = 100,
  onOutputVolumeChange,
  autoVideoOff = false,
  onAutoVideoOffChange,
  autoAudioOff = false,
  onAutoAudioOffChange,
  currentEffect,
  setCurrentEffect,
  speakerDeviceId,
  setSpeakerDeviceId,
  ...props
}) {

  const [connected, setConnected] = useState(false);
  const [connectionState, setConnectionState] = useState(null);
  const { openDrawer, setOpenDrawer } = useVideoConferencesContext();
  useEffect(() => {
    console.log("openDrawer", openDrawer);
  }, [openDrawer]);

  const [showRaiseHand, setShowRaiseHand] = useState(false);
  const [showEmojiReaction, setShowEmojiReaction] = useState(null);
  const [remoteRaisedHands, setRemoteRaisedHands] = useState(new Map());
  
  const [notificationVisible, setNotificationVisible] = useState(false);
  const [notificationContent, setNotificationContent] = useState({});
  const [notificationAction, setNotificationAction] = useState("");
  
  const [remoteEmojiReactions, setRemoteEmojiReactions] = useState(new Map());
  const [showRecording, setShowRecording] = useState(
      props.meetingDetails?.is_recording_active || false
  );

  const [toastNotification, setToastNotification] = useState("");
  const [showToast, setShowToast] = useState(false);
  const [toastStatus, setToastStatus] = useState("");
  
  const [isCoHost, setIsCoHost] = useState(false);
  const [isForceMuteAll, setIsForceMuteAll] = useState(isWebinarMode); // used to disable the mic for all participants
  const [isForceVideoOffAll, setIsForceVideoOffAll] = useState(isWebinarMode); // used to disable the video for all participants
  const [forceMute, setForceMute] = useState(isWebinarMode); // used for host control drawer
  const [forceVideoOff, setForceVideoOff] = useState(isWebinarMode); // used for host control drawer
  const lobbyParticipants = useRef(new Map());
  const [coHostToken, setCoHostToken] = useState(null);
  const [breakoutRooms, setBreakoutRooms] = useState({});
  const [roomKeyCounter, setRoomKeyCounter] = useState(1);
  const [currentRoomName, setCurrentRoomName] = useState("");
  const [isBreakoutRoom, setIsBreakoutRoom] = useState(false);
  const [breakoutRoomDuration, setBreakoutRoomDuration] = useState(5);
  const [endBreakoutRoomTimer, setEndBreakoutRoomTimer] = useState(
      breakoutRoomDuration * 60
  );

  const [frontCameraMirroringPreference, setFrontCameraMirroringPreference] = useState(undefined);
  const [isBreakoutRoomCnfigSet, setIsBreakoutRoomCnfigSet] = useState(false);
  const lastAutoFocusedScreenShareTrack = React.useRef(null);
  const [liveCaptionData, setLiveCaptionData] = useState(null);
  const [privatechatparticipants, setPrivateChatParticipants] = useState([]);
  const [selectedPrivateChatParticipant, setSelectedPrivateChatParticipant] =
      useState(null);
  const [showPrivateChat, setShowPrivateChat] = useState(false);
  const [privatechatmessages, setPrivateChatMessages] = useState(new Map());
  const [newMessageRender, setNewMessageRender] = useState(0);
  const [publicChatUnreadMessagesCount, setPublicChatUnreadMessagesCount] =
      useState(0);
  const [privateChatUnreadMessagesCount, setPrivateChatUnreadMessagesCount] =
      useState(0);
  const [liveCaptionsObject, setLiveCaptionsObject] = useState({
    showIcon:
        props.meetingDetails?.transcription_detail?.transcription_enable ||
        props.isHost ||
        isCoHost,
    isLanguageSelected:
        props.meetingDetails?.transcription_detail?.transcription_enable || false,
    langCode:
        props.meetingDetails?.transcription_detail?.transcription_lang_iso ||
        "en-IN",
  });
  const [finalCaptions, setFinalCaptions] = useState([]);


  const [canDownloadChatAttachment, setCanDownloadChatAttachment] =
      useState(false);
  const [isWhiteboardOpen, setIsWhiteboardOpen] = useState(false);
  const [whiteboardData, setWhiteboardData] = useState({
    elements: [],
    appState: {},
  });
  const [allowLiveCollabWhiteBoard, setAllowLiveCollabWhiteBoard] =
      useState(false);
  const [publicChatMessages, setPublicChatMessages] = useState([]);
  const [isExitWhiteboardModalOpen, setIsExitWhiteboardModalOpen] =
      useState(false);
  const [whiteboardSceneData, setWhiteboardSceneData] = useState({
    start: 0,
    end: 0,
  });
  const [whiteboardId, setWhiteboardId] = useState(null);
  const [screenShareDisplayId, setScreenShareDisplayId] = useState(1);
  // const [isAnnotationEnabled, setIsAnnotationEnabled] = useState(false);
  const [translationDetails, setTranslationDetails] = useState({
    source_lang: undefined,
    target_lang: "",
    text_cap: "",
  });
  const [finalTranslatedCaptions, setFinalTranslatedCaptions] = useState([]);
  // check if screen share is enabled
  const [isScreenShareEnabled, setIsScreenShareEnabled] = useState(false);
  const [screenShareMode, setScreenShareMode] = useState("text");
  const [isFocusTrackEnabled, setIsFocusTrackEnabled] = useState(false);

  const [isRecordingLoading, setIsRecordingLoading] = useState(false);

  // Recording Consent
  const [isRecordingConsentModalOpen, setIsRecordingConsentModalOpen] = useState(false);
  const [participantConsent, setParticipantConsent] = useState([]);
  const [showRecordingConsentDrawer, setShowRecordingConsentDrawer] = useState(false);


  // Pinned messages
  const [privateChatPinnedMessages, setPrivateChatPinnedMessages] = useState(new Map());
  const [publicChatPinnedMessages, setPublicChatPinnedMessages] = useState(null);

  // Brightness
  const [participantBrightness, setParticipantBrightness] = useState(new Map());
  const [brightnessSentTo, setBrightnessSentTo] = useState(new Set());

  // Wake Lock for keeping screen awake
  const wakeLockRef = useRef(null);

  // Wake Lock functions
  const requestWakeLock = useCallback(async () => {
    try {
      if ('wakeLock' in navigator && !wakeLockRef.current) {
        wakeLockRef.current = await navigator.wakeLock.request('screen');
        
        wakeLockRef.current.addEventListener('release', () => {
          console.log('Screen wake lock released');
          wakeLockRef.current = null;
        });
      }
    } catch (error) {
      console.warn('Failed to request screen wake lock:', error);
    }
  }, []);

  const releaseWakeLock = useCallback(async () => {
    try {
      if (wakeLockRef.current) {
        await wakeLockRef.current.release();
        wakeLockRef.current = null;
        console.log('Screen wake lock manually released');
      }
    } catch (error) {
      console.warn('Failed to release screen wake lock:', error);
    }
  }, []);

  // Manage wake lock based on connection state
  useEffect(() => {
    if (connected) {
      requestWakeLock();
    } else {
      releaseWakeLock();
    }

    // Handle page visibility changes
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && connected && !wakeLockRef.current) {
        requestWakeLock();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Cleanup on unmount
    return () => {
      releaseWakeLock();
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [connected, requestWakeLock, releaseWakeLock]);

  const tracks = useTracks(
      [
        { source: Track.Source.Camera, withPlaceholder: true },
        { source: Track.Source.ScreenShare, withPlaceholder: false },
      ],
      { updateOnlyOn: [RoomEvent.ActiveSpeakersChanged], onlySubscribed: false }
  );

  const onScreenShareChange = useCallback(
      (enabled) => {
        setIsScreenShareEnabled(enabled);
      },
      [setIsScreenShareEnabled]
  );

  const layoutContext = useCreateLayoutContext();

  // Memoize expensive track filtering operations
  const screenShareTracks = useMemo(() => 
    tracks
      .filter(isTrackReference)
      .filter((track) => track.publication.source === Track.Source.ScreenShare),
    [tracks]
  );

  const focusTrack = usePinnedTracks(layoutContext)?.[0];
  
  const carouselTracks = useMemo(() => 
    tracks.filter((track) => !isEqualTrackRef(track, focusTrack)),
    [tracks, focusTrack]
  );

  // Socket connection

  const { socket, isSocketConnected } = useSocket({
    keepConnected: isWhiteboardOpen,
    token: props.token,
    connectionUrl: constants.SOCKET_URL,
    user: room?.localParticipant?.participantInfo,
  });

  const fetchBreakoutRoomDetails = useCallback(async () => {
    setTimeout(async () => {
      try {
        if (!room) return;
        const response = await BreakoutRoomService.getBreakoutRoomDetail(
            props.id,
            coHostToken,
            room?.localParticipant?.participantInfo
        );
        if (response?.success === 0) {
          console.log("Error getting breakout room details", response);
          return;
        }

        if (response?.data.length > 0) {
          setBreakoutRooms((prevRooms) => {
            const updatedRooms = { ...prevRooms };
            const allKeys = [];
            
            // Push all the keys of the existing rooms into allKeys
            Object.keys(updatedRooms).forEach((key) => {
              if (updatedRooms[key].manual) {
                allKeys.push(key);
              }
            });
            
            // Process response data
            response.data.forEach((r) => {
              const roomId = r?.name.split("__BR")[1];
              const key = roomId || "0";
              const roomName = roomId ? `Breakout Room ${key}` : "Main Room";
              const participants = r?.participants;

              allKeys.push(key);

              if (updatedRooms[key]) {
                // Update participants of the existing room
                updatedRooms[key].participants = participants;
              } else {
                // Add new room
                updatedRooms[key] = {
                  name: roomName,
                  participants,
                  key,
                };
              }
            });

            // Clean up deleted rooms
            for (let key in updatedRooms) {
              if (
                  !allKeys.includes(key) && // Key not in allKeys array
                  (updatedRooms[key].isDeleted ||
                      !updatedRooms[key].manual || // Manual is false
                      !("manual" in updatedRooms[key])) && // or manual is not present
                  key !== "0" // Key is not "0"
              ) {
                delete updatedRooms[key];
              }
            }
            
            // Ensure the main room is always present
            if (!updatedRooms["0"]) {
              updatedRooms["0"] = {
                name: "Main Room",
                participants: [],
                key: "0",
              };
            }
            
            // Calculate max key for room counter
            const maxKey = Math.max(...allKeys);
            
            // Batch related state updates together
            const activeRooms = Object.keys(updatedRooms).filter(
                (key) =>
                    !updatedRooms[key].isDeleted ||
                    !("isDeleted" in updatedRooms[key])
            );
            
            // Use React's batching to update multiple states together
            if (activeRooms.length <= 1 && openDrawer === DrawerState.BREAKOUTROOM) {
              // Batch these state updates
              setOpenDrawer(DrawerState.PARTICIPANTS);
              setIsBreakoutRoomCnfigSet(false);
            }
            
            // Update room key counter
            setRoomKeyCounter(maxKey + 1);
            
            return updatedRooms;
          });
        }
      } catch (error) {
        // Batch error state updates
        setToastNotification(error.message);
        setToastStatus("error");
        setShowToast(true);
      }
    }, 500);
  }, [
    room, 
    coHostToken, 
    props.id, 
    openDrawer, 
    setIsBreakoutRoomCnfigSet, 
    setRoomKeyCounter, 
  ]);

  // Memoize role to avoid repeated parsing
  const participantRole = useMemo(() => {
    return room?.localParticipant?.metadata
      ? parseMetadata(room?.localParticipant?.metadata)?.role_name
      : null;
  }, [room?.localParticipant?.metadata]);

  // Memoize breakout rooms count to avoid recalculation
  const hasBreakoutRooms = useMemo(() => {
    return Object.keys(breakoutRooms).length > 1;
  }, [breakoutRooms]);

  // Poll breakout room details
  useEffect(() => {
    if (!room || !connected) return;

    const isModerator = participantRole === "moderator";
    const isCohost = participantRole === "cohost" && coHostToken && coHostToken !== "";
    const isModeratorOrCohost = isModerator || isCohost;

    if(!isModeratorOrCohost || !hasBreakoutRooms) return;
    
    const shouldPoll = openDrawer === DrawerState.BREAKOUTROOM;

    let intervalId;

    if (shouldPoll) {
      intervalId = setInterval(fetchBreakoutRoomDetails, 10000);
    }
    // Fetch breakout room details and add event listeners if moderator or cohost with breakout rooms
    fetchBreakoutRoomDetails();
    room.on(RoomEvent.ParticipantConnected, fetchBreakoutRoomDetails);
    room.on(RoomEvent.ParticipantDisconnected, fetchBreakoutRoomDetails);
    
    // Cleanup on unmount or when dependencies change
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
      room.off(RoomEvent.ParticipantConnected, fetchBreakoutRoomDetails);
      room.off(RoomEvent.ParticipantDisconnected, fetchBreakoutRoomDetails);
    };
  }, [
    room,
    connected,
    participantRole,
    coHostToken,
    hasBreakoutRooms,
    openDrawer,
  ]);

  // Handle Event for participant disconnected - OPTIMIZED
  useEffect(() => {
    if (!room) return;

    const onParticipantDisconnected = (participant) => {
      const participantIdentity = participant.identity.toString();

      // Update private chat participants using functional state update
      setPrivateChatParticipants((prevParticipants) => {
        const participantIndex = prevParticipants.findIndex((part) => 
          part.participant.identity.toString() === participantIdentity
        );

        if (participantIndex >= 0) {
          const updatedParticipants = [...prevParticipants];
          updatedParticipants[participantIndex] = {
            ...updatedParticipants[participantIndex],
            isConnected: false,
          };
          return updatedParticipants;
        }
        return prevParticipants; // No change needed
      });

      // Update selected participant using functional state update
      setSelectedPrivateChatParticipant((prevSelected) => {
        if (prevSelected && prevSelected.participant.identity.toString() === participantIdentity) {
          return {
            ...prevSelected,
            isConnected: false,
          };
        }
        return prevSelected; // No change needed
      });

      // Clean up brightness data
      setParticipantBrightness((prev) => {
        const newMap = new Map(prev);
        newMap.delete(participant.identity);
        return newMap;
      });
      setBrightnessSentTo((prev) => {
        const newSet = new Set(prev);
        newSet.delete(participant.identity);
        return newSet;
      });
    };

    room.on(RoomEvent.ParticipantDisconnected, onParticipantDisconnected);

    return () => {
      room.off(RoomEvent.ParticipantDisconnected, onParticipantDisconnected);
    };
  }, [room]); // OPTIMIZED: Removed problematic dependencies

  const sendBrightnessToAll = useCallback(async (brightnessValue) => {
    if (!room || !connected || brightnessValue === 100) return;
    const encoder = new TextEncoder();
    const data = encoder.encode(
      JSON.stringify({
        action: DataReceivedEvent.BRIGHTNESS_CHANGE,
        brightness: brightnessValue,
      })
    );
    await room.localParticipant.publishData(data, { reliable: true });
    // Mark all current participants as having been sent brightness
    const currentParticipants = Array.from(room.remoteParticipants.keys());
    setBrightnessSentTo(new Set(currentParticipants));
  }, [room, connected]);

  const sendBrightnessToParticipant = useCallback(async (participantIdentity, brightnessValue) => {
    if (!room || !connected || brightnessValue === 100) return;

    if (brightnessSentTo.has(participantIdentity)) {
      return;
    }

    const encoder = new TextEncoder();
    const data = encoder.encode(
      JSON.stringify({
        action: DataReceivedEvent.BRIGHTNESS_CHANGE,
        brightness: brightnessValue,
      })
    );
    await room.localParticipant.publishData(data, {
      reliable: true,
      destinationIdentities: [participantIdentity]
    });

    // Mark this participant as having been sent brightness
    setBrightnessSentTo(prev => new Set([...prev, participantIdentity]));
  }, [room, connected, brightnessSentTo]);

  // Enhanced brightness change handler that sends data to other participants
  const handleBrightnessChangeWithDataChannel = useCallback((newBrightness) => {
    // Call the original brightness change handler
    onBrightnessChange(newBrightness);

    // Send brightness to all remote participants via data channel
    sendBrightnessToAll(newBrightness);
  }, [onBrightnessChange, sendBrightnessToAll]);



  // Handle Event for participant connected - OPTIMIZED
  useEffect(() => {
    if (!room) return;

    const onParticipantConnected = (participant) => {
      const participantIdentity = participant.identity.toString();

      // Update private chat participants using functional state update
      setPrivateChatParticipants((prevParticipants) => {
        const participantIndex = prevParticipants.findIndex((part) => 
          part.participant.identity.toString() === participantIdentity
        );

        if (participantIndex >= 0) {
          const updatedParticipants = [...prevParticipants];
          updatedParticipants[participantIndex] = {
            ...updatedParticipants[participantIndex],
            isConnected: true,
          };
          return updatedParticipants;
        }
        return prevParticipants; // No change needed
      });

      // Update selected participant using functional state update
      setSelectedPrivateChatParticipant((prevSelected) => {
        if (prevSelected && prevSelected.participant.identity.toString() === participantIdentity) {
          return {
            ...prevSelected,
            isConnected: true,
          };
        }
        return prevSelected; // No change needed
      });

      // Handle brightness for new participant - use ref to get current value
      setTimeout(() => {
        // Access brightness from props (it's passed down from parent)
        // Since brightness is a prop, we can access it directly in the closure
        const currentBrightness = brightness;
        if (currentBrightness !== 100) {
          sendBrightnessToParticipant(participant.identity, currentBrightness);
          sendBrightnessToAll(currentBrightness);
        }
      }, 500);
    };

    room.on(RoomEvent.ParticipantConnected, onParticipantConnected);

    return () => {
      room.off(RoomEvent.ParticipantConnected, onParticipantConnected);
    };
  }, [room, brightness, sendBrightnessToParticipant, sendBrightnessToAll]); // OPTIMIZED: Only essential dependencies

  // Check if meeting is finished
  useEffect(() => {
    if (!room || meetingFeatures?.is_basic === 0) return;
    const endDateTime = moment(props.meetingDetails?.end_date);
    const intervalId = setInterval(() => {
      const currentDateTime = moment();
      const timeDifference = endDateTime.diff(currentDateTime, "seconds");

      if (timeDifference <= 0) {
        props.setIsMeetingFinished(() => true);
        setLocalStorage(constants.CO_HOST_TOKEN, null);
        room.disconnect();
        if (isElectronApp) {
          window?.electronAPI?.ipcRenderer?.send("stop-annotation");
        }
      } else if (timeDifference <= 300 && timeDifference >= 280) {
        // 5 minutes in seconds
        setToastNotification(`Meeting will end in 5 minutes.`);
        setToastStatus("warning");
        setShowToast(true);
      }
    }, 30000); // Run every 30sec

    return () => clearInterval(intervalId); // Cleanup interval on unmount
  }, [room]);

  useEffect(() => {
    // If screen share tracks are published, and no pin is set explicitly, auto set the screen share.
    if (
        screenShareTracks.some((track) => track.publication.isSubscribed) &&
        lastAutoFocusedScreenShareTrack.current === null
    ) {
      log.debug("Auto set screen share focus:", {
        newScreenShareTrack: screenShareTracks[0],
      });
      layoutContext.pin.dispatch?.({
        msg: "set_pin",
        trackReference: screenShareTracks[0],
      });
      lastAutoFocusedScreenShareTrack.current = screenShareTracks[0]; // eslint-disable-line
      if (isElectronApp && isScreenShareEnabled) {
        let userChoice = getLocalStorage(constants.MEETING_USER_CHOICES);
        window?.electronAPI?.ipcRenderer?.send("minimize-main-window", {
          video: userChoice.video,
          audio: userChoice.audio,
          screenShareDisplayId,
        });
      }
    } else if (
        lastAutoFocusedScreenShareTrack.current &&
        !screenShareTracks.some(
            (track) =>
                track.publication.trackSid ===
                lastAutoFocusedScreenShareTrack.current?.publication?.trackSid
        )
    ) {
      log.debug("Auto clearing screen share focus.");
      layoutContext.pin.dispatch?.({ msg: "clear_pin" });
      lastAutoFocusedScreenShareTrack.current = null;
      if (isElectronApp) {
        window?.electronAPI?.ipcRenderer?.send("stop-annotation");
      }
    }
    if (focusTrack && !isTrackReference(focusTrack)) {
      const updatedFocusTrack = tracks.find(
          (tr) =>
              tr.participant.identity === focusTrack.participant.identity &&
              tr.source === focusTrack.source
      );
      if (
          updatedFocusTrack !== focusTrack &&
          isTrackReference(updatedFocusTrack)
      ) {
        layoutContext.pin.dispatch?.({
          msg: "set_pin",
          trackReference: updatedFocusTrack,
        });
      }
    }
  }, [
    screenShareTracks,
    focusTrack?.publication?.trackSid,
    tracks,
    layoutContext.pin,
    isElectronApp,
    isScreenShareEnabled,
    screenShareDisplayId,
  ]);

  useEffect(() => {
    if (!room) return;
    if (!connected) return;
    
    const publishRaiseHandData = async () => {
      const encoder = new TextEncoder();
      const data = encoder.encode(
          JSON.stringify({
            action: showRaiseHand
                ? DataReceivedEvent.RAISE_HAND
                : DataReceivedEvent.STOP_RAISE_HAND,
          })
      );
      await room.localParticipant.publishData(data, { reliable: true });
    };
    
    publishRaiseHandData();
  }, [showRaiseHand, room, connected]);

  useEffect(() => {
    if (!room || !connected || !showEmojiReaction) return;
    
    const publishEmojiReactionData = async () => {
      const encoder = new TextEncoder();
      const data = encoder.encode(
          JSON.stringify({
            action: showEmojiReaction,
          })
      );
      await room.localParticipant.publishData(data, { reliable: true });
    };

    publishEmojiReactionData();
  }, [showEmojiReaction, room, connected]);

  // Memoize data handlers to prevent recreation on every render
  const dataHandlers = useMemo(() => {
    
    return {
      handleRaiseHand: (participant) => {
        setRemoteRaisedHands((prev) => new Map(prev).set(participant.identity, true));
      },
      
      handleStopRaiseHand: (participant) => {
        setRemoteRaisedHands((prev) => {
          const newMap = new Map(prev);
          newMap.delete(participant?.identity);
          return newMap;
        });
      },
      
      handleStopRaiseHandAll: () => {
        setRemoteRaisedHands(new Map());
        setShowRaiseHand(false);
      },
      
      handleEmojiReaction: (participant, actionType) => {
        setRemoteEmojiReactions((prev) => new Map(prev).set(participant.identity, actionType));
      },
      
      // Add more handlers as needed...
    };
  }, [setRemoteRaisedHands, setShowRaiseHand, setRemoteEmojiReactions]);

  // Data Received
  useEffect(() => {
    if (!room) return;

    const decoder = new TextDecoder();
    const onDataReceived = async (payload, participant, kind) => {
      const strData = decoder.decode(payload);
      const data = JSON.parse(strData);
      if (data.action && data.action === DataReceivedEvent.RAISE_HAND) {
        dataHandlers.handleRaiseHand(participant);
      } else if (
          data.action &&
          data.action === DataReceivedEvent.STOP_RAISE_HAND
      ) {
        dataHandlers.handleStopRaiseHand(participant);
      } else if (
          data.action &&
          data.action === DataReceivedEvent.STOP_RAISE_HAND_ALL
      ) {
        dataHandlers.handleStopRaiseHandAll();
      } else if (
          data.action &&
          data.action === DataReceivedEvent.ASK_TO_UNMUTE_MIC
      ) {
        // Ask to unmute mic
        setNotificationContent({
          title: `${participant.name} is asking you to turn on your mic`,
        });
        setNotificationAction("unmute");
        setNotificationVisible(true);
        if (isForceMuteAll) {
          setIsForceMuteAll(false);
        }
      } else if (data.action && data.action === DataReceivedEvent.MUTE_MIC) {
        room.localParticipant.setMicrophoneEnabled(false);
        setToastNotification("Microphone muted!");
        setToastStatus("info");
        setShowToast(true);
        // Mute mic
      } else if (
          data.action &&
          data.action === DataReceivedEvent.ASK_TO_UNMUTE_CAMERA
      ) {
        // Ask to unmute camera
        setNotificationContent({
          title: `${participant.name} is asking you to turn on your camera`,
        });
        setNotificationAction("videoOn");
        setNotificationVisible(true);
        if (isForceVideoOffAll) {
          setIsForceVideoOffAll(false);
        }
      } else if (data.action && data.action === DataReceivedEvent.MUTE_CAMERA) {
        room.localParticipant.setCameraEnabled(false);
        setToastNotification("Camera off!");
        setToastStatus("info");
        setShowToast(true);
      } else if (
          data.action &&
          data.action === DataReceivedEvent.SEND_PRIVATE_MESSAGE
      ) {
        const { message, id, timestamp, isReplied, replyMessage } = data;
        // Find the participant in the private chat message map using identity
        const from = privatechatmessages.get(participant.identity);
        if (!from) {
          privatechatmessages.set(participant.identity, [
            {
              from: participant,
              message,
              id,
              timestamp,
              isReplied,
              replyMessage,
              reactions: [],
            },
          ]);
        } else {
          from.push({
            from: participant,
            message,
            id,
            timestamp,
            isReplied,
            replyMessage,
            reactions: [],
          });
        }
        setPrivateChatMessages(privatechatmessages);

        if (privatechatparticipants.length === 0) {
          setSelectedPrivateChatParticipant({
            key: 1,
            participant,
            isConnected: true,
            receivedUnreadMessagesCount: 1,
          });
          setPrivateChatParticipants([
            {
              key: 1,
              participant,
              isConnected: true,
              receivedUnreadMessagesCount: 1,
            },
          ]);
          setPrivateChatUnreadMessagesCount((prev) => prev + 1);
        } else {
          const participantIndex = privatechatparticipants.findIndex((part) => {
            return part.participant.identity === participant.identity;
          });
          if (participantIndex < 0) {
            const maxKey = privatechatparticipants.reduce(
                (max, p) => Math.max(max, p.key),
                0
            );
            const newParticipant = {
              key: maxKey + 1,
              participant,
              isConnected: true,
              receivedUnreadMessagesCount: 1,
            };
            setPrivateChatParticipants([
              ...privatechatparticipants,
              newParticipant,
            ]);
            setPrivateChatUnreadMessagesCount((prev) => prev + 1);
          } else {
            const updatedParticipants = [...privatechatparticipants];

            if (
                selectedPrivateChatParticipant?.participant.identity !==
                participant.identity
            ) {
              updatedParticipants[participantIndex] = {
                ...updatedParticipants[participantIndex],
                receivedUnreadMessagesCount:
                    updatedParticipants[participantIndex]
                        .receivedUnreadMessagesCount + 1,
              };
              setPrivateChatUnreadMessagesCount((prev) => prev + 1);
            } else if (!showPrivateChat) {
              const updatedSelectedParticipant = {
                ...selectedPrivateChatParticipant,
                receivedUnreadMessagesCount:
                    selectedPrivateChatParticipant.receivedUnreadMessagesCount +
                    1,
              };
              setSelectedPrivateChatParticipant(updatedSelectedParticipant);

              updatedParticipants[participantIndex] = {
                ...updatedParticipants[participantIndex],
                receivedUnreadMessagesCount:
                    updatedParticipants[participantIndex]
                        .receivedUnreadMessagesCount + 1,
              };
              setPrivateChatUnreadMessagesCount((prev) => prev + 1);
            }

            setPrivateChatParticipants(updatedParticipants);
          }
        }

        setNewMessageRender((prev) => prev + 1);
      } else if (
          data.action &&
          data.action === DataReceivedEvent.SEND_PUBLIC_MESSAGE
      ) {
        const { message, id, timestamp, isReplied, replyMessage } = data;
        const mesg = {
          id: id,
          message: message,
          timestamp: timestamp,
          from: participant,
          isReplied,
          replyMessage,
        };
        setPublicChatMessages((prev) => [...prev, mesg]);
        setPublicChatUnreadMessagesCount((prev) => prev + 1);
      } else if (data.action && data.action === DataReceivedEvent.HEART) {
        dataHandlers.handleEmojiReaction(participant, DataReceivedEvent.HEART);
      } else if (data.action && data.action === DataReceivedEvent.BLUSH) {
        dataHandlers.handleEmojiReaction(participant, DataReceivedEvent.BLUSH);
      } else if (data.action && data.action === DataReceivedEvent.CLAP) {
        dataHandlers.handleEmojiReaction(participant, DataReceivedEvent.CLAP);
      } else if (data.action && data.action === DataReceivedEvent.SMILE) {
        dataHandlers.handleEmojiReaction(participant, DataReceivedEvent.SMILE);
      } else if (data.action && data.action === DataReceivedEvent.THUMBS_UP) {
        dataHandlers.handleEmojiReaction(participant, DataReceivedEvent.THUMBS_UP);
      } else if (
          data.action &&
          data.action === DataReceivedEvent.GRINNING_FACE
      ) {
        dataHandlers.handleEmojiReaction(participant, DataReceivedEvent.GRINNING_FACE);
      } else if (
          data.action &&
          data.action === DataReceivedEvent.MAKE_CO_HOST
      ) {
        setForceMute(data?.forceMute ?? forceMute);
        setForceVideoOff(data?.forceVideoOff ?? forceVideoOff);
        setCoHostToken(data.token);
        const tokenDetails = {
          token: data.token,
          meetingId: props.id,
          meeting_attendance_id: parseMetadata(room.localParticipant.metadata)
              ?.meeting_attendance_id,
          current_session_uid: parseInt(
              parseMetadata(room.localParticipant.metadata)?.current_session_uid,
              10
          ),
        };
        setLocalStorage(constants.CO_HOST_TOKEN, tokenDetails);
        setBreakoutRooms(data?.breakoutRooms ?? breakoutRooms);
        setAllowLiveCollabWhiteBoard(
            data?.allowLiveCollabWhiteBoard ?? allowLiveCollabWhiteBoard
        );
      } else if (
          data.action &&
          data.action === DataReceivedEvent.REMOVE_CO_HOST
      ) {
        setCoHostToken(null);
        setLocalStorage(constants.CO_HOST_TOKEN, null);
      } else if (data.action && data.action === DataReceivedEvent.LOBBY) {
        if (
            props.isHost ||
            parseMetadata(room.localParticipant.metadata)?.role_name === "cohost"
        ) {
          const updatedMap = new Map(lobbyParticipants.current);
          const currentTime = Date.now();

          if (updatedMap.has(data.request_id)) {
            const existingData = updatedMap.get(data.request_id);
            updatedMap.set(data.request_id, {
              ...existingData,
              time: currentTime,
            });
          } else {
            updatedMap.set(data.request_id, { ...data, time: currentTime });
            // setToastNotification(`${data.display_name} wants to join`);
            setToastStatus("content");
            setToastNotification(
              <AllowParticipant
                participantName={data.display_name}
                requestId={data.request_id}
                id={props.id}
                setShowToast={setShowToast}
                coHostToken={coHostToken}
                localParticipant={room?.localParticipant}
                setToastNotification={setToastNotification}
                setToastStatus={setToastStatus}
                removeParticipantFromLobby={removeParticipantFromLobby}
              />
            );
            setShowToast(true);
            new Audio(lobbyNotification).play();
          }

          lobbyParticipants.current = updatedMap;
        }
      } else if (
          data.action &&
          data.action === DataReceivedEvent.FORCE_MUTE_ALL
      ) {
        if (
            parseMetadata(room.localParticipant.metadata)?.role_name !== "cohost" &&
            parseMetadata(room.localParticipant.metadata)?.role_name !== "moderator"
        ) {
          room.localParticipant.setMicrophoneEnabled(false);
          setIsForceMuteAll(data.value);
        }
        setForceMute(data.value);
      } else if (
          data.action &&
          data.action === DataReceivedEvent.FORCE_VIDEO_OFF_ALL
      ) {
        if (
            parseMetadata(room.localParticipant.metadata)?.role_name !== "cohost" &&
            parseMetadata(room.localParticipant.metadata)?.role_name !== "moderator"
        ) {
          room.localParticipant.setCameraEnabled(false);
          setIsForceVideoOffAll(data.value);
        }
        setForceVideoOff(data.value);
      } else if (
          data.action &&
          data.action === DataReceivedEvent.BR_MOVE_PARTICIPANT
      ) {
        try {
          if (data.participant_id.includes(room.localParticipant.identity)) {
            const response = await BreakoutRoomService.getBreakoutRoomToken(
                room.localParticipant,
                `${data?.to_meeting_uid}`
            );
            if (response?.success === 0) {
              console.log("Error getting breakout room token", response);
              return;
            }
            if (response?.data?.access_token) {
              setIsUsingBreakoutRoom(true);
              setIsMovingToRoom(true);
              setMovingRoomToken(response?.data?.access_token?.token);
              if (
                  parseMetadata(room?.localParticipant?.metadata)?.role_name ===
                  "moderator" ||
                  parseMetadata(room?.localParticipant?.metadata)?.role_name ===
                  "cohost"
              ) {
                await fetchBreakoutRoomDetails();
              }
            }
          }
        } catch (error) {
          setToastNotification(error.message);
          setToastStatus("error");
          setShowToast(true);
          // console.log("Error getting breakout room token", error);
        }
      } else if (
          data.action &&
          data.action === DataReceivedEvent.BREAKOUT_ROOM_UPDATE
      ) {
        if (
            parseMetadata(room?.localParticipant?.metadata)?.role_name ===
            "moderator" ||
            parseMetadata(room?.localParticipant?.metadata)?.role_name === "cohost"
        ) {
          let activeRoom = 0;
          setBreakoutRooms(data.breakoutRooms);
          if (Object.keys(data.breakoutRooms).length <= 2) {
            // Use Object.keys to iterate only over own properties
            Object.keys(data.breakoutRooms).forEach((key) => {
              if (!data.breakoutRooms[key].isDeleted) {
                activeRoom += 1;
              }
            });
            if (activeRoom <= 1) {
              setOpenDrawer(DrawerState.PARTICIPANTS);
              setIsBreakoutRoomCnfigSet(false);
            }
          }
        }
      } else if (data.action && data.action === DataReceivedEvent.LIVECAPTION) {
        setLiveCaptionData(data);
        setLiveCaptionsObject((prev) => ({
          ...prev,
          showIcon: true,
        }));
      } else if (
          data.action &&
          data.action === DataReceivedEvent.SHOW_LIVECAPTION
      ) {
        // Data Received for live caption
        setLiveCaptionsObject(prev => ({
          ...prev,
          showIcon: data.liveCaptionsData.showIcon,
          isLanguageSelected: prev.isLanguageSelected || data.liveCaptionsData.isLanguageSelected,
          langCode: data.liveCaptionsData.langCode,
        }));
        setOpenDrawer(DrawerState.LIVECAPTION);
        setToastNotification("Live caption is enabled");
        setToastStatus("info");
        setShowToast(true);
      } else if (
          data.action &&
          data.action === DataReceivedEvent.REQUEST_LIVECAPTION_DRAWER_STATE
      ) {
        if (liveCaptionsObject.showIcon) {
          const encoder = new TextEncoder();
          const data = encoder.encode(
              JSON.stringify({
                action: DataReceivedEvent.SHOW_LIVECAPTION,
                liveCaptionsData: { ...liveCaptionsObject },
              })
          );
          await room?.localParticipant.publishData(data, {
            reliable: true,
            destinationIdentities: [participant?.identity],
          });
        }
      } else if (
          data.action &&
          data.action === DataReceivedEvent.CAN_DOWNLOAD_CHAT_ATTACHEMENT
      ) {
        setCanDownloadChatAttachment(data.value);
      } else if (
          data.action &&
          data.action === DataReceivedEvent.WHITEBOARD_STATE
      ) {
        setToastNotification("Whiteboard Opened");
        setShowToast(true);
        setToastStatus("success");
        setIsWhiteboardOpen(data?.value);
      } else if (
          data.action &&
          data.action === DataReceivedEvent.ALLOW_LIVE_COLLAB_WHITEBOARD
      ) {
        setAllowLiveCollabWhiteBoard(data.value);
      } else if (
          data.action &&
          data.action === DataReceivedEvent.WHITEBOARD_DATA_REQUEST
      ) {
        if (isSocketConnected) {
          setTimeout(() => {
            socket.emit(SocketChannel.WHITEBOARD_UPDATE, {
              elements: whiteboardData?.elements,
            });
          }, 500); // 500 milliseconds delay
        }
      } else if (
        data.action &&
        data.action === DataReceivedEvent.ADD_REACTION
      ) {
        const { messageId, reaction, removeReaction, mode } = data;
        console.log("data ADD_REACTION", data);
        if(mode === "public"){
          setPublicChatMessages(prevMessages =>
            prevMessages.map(msg =>
              msg.id === messageId
                ? {
                    ...msg,
                    reactions: removeReaction
                      ? msg.reactions.filter(r => r.reactor !== reaction.reactor)
                      : msg.reactions?.some(r => r.reactor === reaction.reactor)
                        ? msg.reactions.filter(r => r.reactor !== reaction.reactor).concat([reaction])
                        : [...(msg.reactions || []), reaction]
                  }
                : msg
            )
          );
        } else if(mode === "private"){
          console.log("privatechatmessages before", privatechatmessages);
          const from = privatechatmessages.get(participant.identity);
          if (from) {
            const messageIndex = from.findIndex(msg => msg.id === messageId);
            console.log("messageIndex", messageIndex);
            if (messageIndex !== -1) {
              from[messageIndex] = {
                ...from[messageIndex],
                reactions: removeReaction
                  ? from[messageIndex].reactions.filter(r => r.reactor !== reaction.reactor)
                  : from[messageIndex].reactions?.some(r => r.reactor === reaction.reactor)
                    ? from[messageIndex].reactions.filter(r => r.reactor !== reaction.reactor).concat([reaction])
                    : [...(from[messageIndex].reactions || []), reaction],
              };
              privatechatmessages.set(participant.identity, from);
              console.log("from[messageIndex]", from[messageIndex]);
            }
          }
          setPrivateChatMessages(new Map(privatechatmessages));
          setNewMessageRender((prev) => prev + 1);
          console.log("privatechatmessages after", privatechatmessages);
        }
      } else if (
        data.action &&
        data.action === DataReceivedEvent.EDIT_MESSAGE
      ) {
        console.log("Data Received for EDIT_MESSAGE", data);
        if(data.mode === "public"){
          setPublicChatMessages(prevMessages =>
          prevMessages.map(msg =>
            msg.id === data.id
              ? {
                  ...msg,
                  message: data.message,
                  editTimestamp: data.timestamp,
                  timestamp: data.timestamp
                }
              : msg
          )
        )}
        if(data.mode === "private"){
          const from = privatechatmessages.get(participant.identity);
          if (from) {
            const messageIndex = from.findIndex(msg => msg.id === data.id);
            console.log("messageIndex", messageIndex);
            if (messageIndex !== -1) {
              from[messageIndex] = {
                ...from[messageIndex],
                message: data.message,
                timestamp: data.timestamp,
                editTimestamp: data.timestamp
              };
            }
          }
          setPrivateChatMessages(new Map(privatechatmessages));
          setNewMessageRender((prev) => prev + 1);
        }
      } else if (
        data.action &&
        data.action === DataReceivedEvent.DELETE_MESSAGE
      ) {
        console.log("Data Received for DELETE_MESSAGE", data);
        if(data.mode === "public"){
          setPublicChatMessages(prevMessages => prevMessages.map(msg => msg.id === data.id ? { ...msg, deleted: true } : msg));
        } else if(data.mode === "private"){
          const from = privatechatmessages.get(participant.identity);
          if (from) {
            const messageIndex = from.findIndex(msg => msg.id === data.id);
            if (messageIndex !== -1) {
              from[messageIndex] = {
                ...from[messageIndex],
                deleted: true
              };
            }
          }
          setPrivateChatMessages(new Map(privatechatmessages));
        }
      } else if(data.action && data.action === DataReceivedEvent.RECORDING_CONSENT_MODAL){
        setIsRecordingConsentModalOpen(data.value);
      }else if (data.action && data.action === DataReceivedEvent.RECORDING_CONSENT_STATUS){
        // Find the participant in the participantConsent array
        const participantIndex = participantConsent.findIndex(p => p.participantId === participant?.identity);

        if (participantIndex !== -1) {
          // Update existing participant's consent status
          const updatedConsent = [...participantConsent];
          updatedConsent[participantIndex] = {
            ...updatedConsent[participantIndex],
            consent: data.consent
          };
          setParticipantConsent(updatedConsent);
        } else {
          // Add new participant with consent status
          setParticipantConsent(prev => [...prev, {
            participantName: participant?.name,
            participantId: participant?.identity,
            consent: data.consent
          }]);
        }
      } else if (
        data.action &&
        data.action === DataReceivedEvent.SCREEN_CAPTURE_TAKEN
      ) {
        // Handle screen capture notification
        const capturerName = data.participantName || participant?.name || participant?.identity || 'Someone';

        console.log("Screen capture taken by:", capturerName);

        // Show toast notification to all participants
        setToastNotification(`${capturerName} captured the screenshot`);
        setToastStatus("info");
        setShowToast(true);
      } else if (
        data.action &&
        data.action === DataReceivedEvent.BRIGHTNESS_CHANGE
      ) {
        // Handle brightness change from remote participant
        const { brightness: remoteBrightness } = data;

        setParticipantBrightness((prev) => {
          const newMap = new Map(prev);
          newMap.set(participant.identity, remoteBrightness);
          return newMap;
        });
      } else if (
        data.action &&
        data.action === DataReceivedEvent.FETCH_PARTICIPANTS_BRIGHTNESS
      ) {
        // If this is a request (no participantsBrightness field), respond with our brightness
        if (!data.participantsBrightness) {
          const encoder = new TextEncoder();
          const response = encoder.encode(
            JSON.stringify({
              action: DataReceivedEvent.FETCH_PARTICIPANTS_BRIGHTNESS,
              participantsBrightness: {
                [room.localParticipant.identity]: brightness,
              },
            })
          );
          await room.localParticipant.publishData(response, {
            reliable: true,
            destinationIdentities: [data.identity],
          });
        } else {
          // This is a response, merge into our map
          const { participantsBrightness } = data;
          setParticipantBrightness((prev) => {
            const newMap = new Map(prev);
            Object.entries(participantsBrightness).forEach(([id, value]) => {
              newMap.set(id, value);
            });
            return newMap;
          });
        }
      }
    };
    room.on(RoomEvent.DataReceived, onDataReceived);
    return () => {
      room.off(RoomEvent.DataReceived, onDataReceived);
    };
  }, [
    room,
    dataHandlers,
    isCoHost,
    coHostToken,
    privatechatmessages,
    privateChatUnreadMessagesCount,
    selectedPrivateChatParticipant,
    whiteboardData,
    isSocketConnected,
    socket,
    liveCaptionsObject,
    participantConsent
  ]);

  useEffect(() => {
    if (!room) return;

    const handleTextStream = async (reader, participantInfo) => {
      try {
        const message = await reader.readAll();
        const handler = operationHandlers[message];

        if (handler) {
          handler(room, setToastNotification, setToastStatus, setShowToast);
        }
      } catch (error) {
        console.log("error", error);
      }
    };

    room.registerTextStreamHandler(TOPIC_NAME.AGENT_OPERATION, handleTextStream);
    return () => {
        room.unregisterTextStreamHandler(TOPIC_NAME.AGENT_OPERATION);
    };
  }, [room]);

  // UseEffect for the chat drawer to listen agent messages
  useEffect(() => {
    if (!room) return;

    const handleChatStream = async (reader, participantInfo) => {
      const message = await reader.readAll();
      if (reader.info.attributes['lk.transcribed_track_id']) {
        if (reader.info.attributes['lk.transcription_final'] === 'true') {
          const trackId = reader.info.attributes['lk.transcribed_track_id'];
          const participants = Array.from(room.remoteParticipants.values());
          participants.push(room.localParticipant);

          // Find the participant by matching the track ID
          const participant = participants.find(p => {
            const tracks = Array.from(p.trackPublications.values());
            return tracks.some(track => track.trackSid === trackId);
          });


          const isLocalParticipant = participant?.identity === room.localParticipant.identity;
          const participantType = isLocalParticipant ? "Local" : "Remote";
          const displayName = isLocalParticipant 
            ? `You (${room.localParticipant.name})` 
            : participant?.name || participantInfo.identity;


          const transcriptionMessage = {
            id: generateUUID(),
            message: message,
            timestamp: Date.now(),
            from: participant || participantInfo,
            isTranscription: true,
            trackId: trackId,
            segmentId: reader.info.attributes['lk.segment_id']
          };

          publicChatMessages.push(transcriptionMessage);
          setPublicChatMessages([...publicChatMessages]);

          console.log(`[Transcription] ${participantType} Participant (${displayName}) [ID: ${participant?.identity || participantInfo.identity}] - Track: ${trackId}, Segment: ${reader.info.attributes['lk.segment_id']} - ${message}`);
        }
      } else {
  
        const participants = Array.from(room.remoteParticipants.values());
        participants.push(room.localParticipant);
        
        // Find the participant by identity
        const participant = participants.find(p => p.identity === participantInfo.identity);
        
        const isLocalParticipant = participantInfo.identity === room.localParticipant.identity;
        const participantType = isLocalParticipant ? "Local" : "Remote";
        const displayName = isLocalParticipant 
          ? `You (${room.localParticipant.name})` 
          : participant?.name || participantInfo.identity;

        console.log(`[Message] ${participantType} Participant (${displayName}) [ID: ${participant?.identity || participantInfo.identity}] - ${message}`);
      }
    };

    room.registerTextStreamHandler(TOPIC_NAME.AGENT_TRANSCRIPTION, handleChatStream);
    room.registerTextStreamHandler(TOPIC_NAME.CHAT, handleChatStream);


    return () => {
      room.unregisterTextStreamHandler(TOPIC_NAME.AGENT_TRANSCRIPTION);
      room.unregisterTextStreamHandler(TOPIC_NAME.CHAT);
    };
  }, [room]);

  useEffect(() => {
    if (isElectronApp) {
      window.electronAPI.ipcRenderer.on("track-control", (e, data) => {
        if (data.action === PROCESS_EVENT.MIC) {
          room.localParticipant.setMicrophoneEnabled(data.value);
        } else if (data.action === PROCESS_EVENT.CAMERA) {
          room.localParticipant.setCameraEnabled(data.value);
        }
      });
    }
  }, []);


  useEffect(() => {
    if (!room) return;
    const onParticipantMetadataChanged = (_, participant) => {
      if (
          parseMetadata(participant.metadata)?.role_name === "cohost" &&
          room.localParticipant.identity === participant.identity
      ) {
        setIsCoHost(true);
        setLiveCaptionsObject((prev) => ({
          ...prev,
          showIcon: true,
        }));
      } else if (
          parseMetadata(participant.metadata)?.role_name === "participant" &&
          room.localParticipant.identity === participant.identity
      ) {
        setIsCoHost(false);
      }
    };
    room.on(RoomEvent.ParticipantMetadataChanged, onParticipantMetadataChanged);
    return () => {
      room.off(
          RoomEvent.ParticipantMetadataChanged,
          onParticipantMetadataChanged
      );
    };
  }, [room]);


  useEffect(() => {
    if (!room) return;
    const onConnectionStateChange = async (state) => {
      setConnected(state === ConnectionState.Connected);
      setConnectionState(state);

      // Handle brightness state based on connection state
      if (state === ConnectionState.Connected) {
        // Broadcast a request for all participants' brightness
        const encoder = new TextEncoder();
        const data = encoder.encode(
          JSON.stringify({
            action: DataReceivedEvent.FETCH_PARTICIPANTS_BRIGHTNESS,
            identity: room.localParticipant.identity
          })
        );
        await room.localParticipant.publishData(data, { reliable: true });

        // Immediately broadcast your own brightness to everyone
        if (brightness !== 100) {
          const brightnessData = encoder.encode(
            JSON.stringify({
              action: DataReceivedEvent.FETCH_PARTICIPANTS_BRIGHTNESS,
              participantsBrightness: {
                [room.localParticipant.identity]: brightness,
              },
            })
          );
          await room.localParticipant.publishData(brightnessData, { reliable: true });
        }
      } else if (state === ConnectionState.Disconnected) {
        setParticipantBrightness(new Map());
        setBrightnessSentTo(new Set());
      }

      if (
        state === ConnectionState.Reconnecting ||
        state === ConnectionState.SignalReconnecting
      ) {
        datadogLogs.logger.error('Reconnecting to the room!!!',{
          meetingId: props.id,
          room: room,
          participant: room?.localParticipant,
        });
      }
    };
    room.on(RoomEvent.ConnectionStateChanged, onConnectionStateChange);
    return () => {
      room.off(RoomEvent.ConnectionStateChanged, onConnectionStateChange);
    };
  }, [room]);



  // Disconnect reason
  useEffect(() => {
    const onDisconnected = (disconnectReason) => {
      // Clear brightness state on disconnect
      setParticipantBrightness(new Map());
      setBrightnessSentTo(new Set());
      switch (disconnectReason) {
        case DisconnectReason.CLIENT_INITIATED:
          datadogLogs.logger.error('DISCONNECT REASON: CLIENT_INITIATED',{
            meetingId: props.id,
            room: room,
            participant: room?.localParticipant,
          });
          if (props.isMeetingFinished) window.location.href = "/meeting/ended";
          else window.location.href = "/meeting/left";
          break;
        case DisconnectReason.ROOM_DELETED:
          datadogLogs.logger.error('DISCONNECT REASON: ROOM_DELETED',{
            meetingId: props.id,
            room: room,
            participant: room?.localParticipant,
          });
          window.location.href = "/meeting/ended";
          break;
        case DisconnectReason.PARTICIPANT_REMOVED:
          datadogLogs.logger.error('DISCONNECT REASON: PARTICIPANT_REMOVED',{
            meetingId: props.id,
            room: room,
            participant: room?.localParticipant,
          });
          window.location.href = "/meeting/removed";
          break;
        case DisconnectReason.UNKNOWN_REASON:
          datadogLogs.logger.error('DISCONNECT REASON: UNKNOWN_REASON',{
            meetingId: props.id,
            room: room,
            participant: room?.localParticipant,
            disconnectReason: disconnectReason,
          });
          window.location.href = "/meeting/left";
          break;
        default:
          datadogLogs.logger.error('DISCONNECT REASON: DEFAULT',{
            meetingId: props.id,
            room: room,
            participant: room?.localParticipant,
            disconnectReason: disconnectReason,
          });
          window.location.href = "/meeting/left";
      }
    };
    room.on(RoomEvent.Disconnected, onDisconnected);
    return () => {
      room.off(RoomEvent.Disconnected, onDisconnected);
    };
  }, [room]);

  const fetchBreakoutRoomConfig = async () => {
    try {
      const response = await BreakoutRoomService.getBreakoutRoomConfig(
          props.meetingDetails?.room_uid,
          coHostToken,
          room?.localParticipant?.participantInfo
      );
      if (response?.success === 0) {
        console.log("Error getting breakout room config", response);
        return;
      }
      return response;
    } catch (error) {
      setToastNotification(error.message);
      setToastStatus("error");
      setShowToast(true);
    }
  };

  // Initial Use Effect
  const breakoutRoomEndTime = useRef(null);

  useEffect(() => {
    if (!room || !connected) return;
    const fetchConfig = async () => {
      // set the room name
        if (
            parseMetadata(room?.localParticipant?.metadata)?.role_name ===
            "moderator"
        ) {
          await fetchBreakoutRoomDetails();
        }
        const roomNumber = room?.roomInfo?.name.split("__BR")[1];
        const roomkey = roomNumber || "0";
        const roomName = roomNumber ? `Breakout Room ${roomkey}` : "Main Room";
        setCurrentRoomName(roomName);
        setIsBreakoutRoom(!!roomNumber);
        if (
            parseMetadata(room?.localParticipant?.metadata)?.role_name === "cohost"
        ) {
          const tokenData = getLocalStorage(constants.CO_HOST_TOKEN);
          if (
              tokenData &&
              tokenData.token &&
              tokenData.meetingId === props.id
          ) {
            setCoHostToken(tokenData.token);
            setIsCoHost(true);
          }
        }
        if (roomkey !== "0") {
          const response = await fetchBreakoutRoomConfig();
          if (response?.success === 1) {
            response?.data?.forEach((config) => {
              if (config?.room_uid === room?.roomInfo?.name) {
                const startTime = moment(config?.start_datetime).local();

                const currentTime = moment();

                const differenceInSeconds = currentTime.diff(
                    startTime,
                    "seconds"
                );
                setBreakoutRoomDuration(config?.auto_timeout);
                const timeoutSeconds = config?.auto_timeout * 60;
                const leftTimeout = timeoutSeconds - differenceInSeconds;

                if (leftTimeout > 0) {
                  setEndBreakoutRoomTimer(leftTimeout);
                  breakoutRoomEndTime.current = moment().add(
                      leftTimeout,
                      "seconds"
                  );
                } else {
                  // setBreakoutRoomDuration(0);
                  breakoutRoomEndTime.current = 0;
                  setEndBreakoutRoomTimer(0);
                }
              }
            });
          }
        } else {
          const response = await fetchBreakoutRoomConfig();
          if (response?.success === 1) {
            setBreakoutRoomDuration(response?.data[0]?.auto_timeout || 5);
            if (
                response?.data[0]?.auto_timeout &&
                Object.keys(breakoutRooms).length > 1
            ) {
              setIsBreakoutRoomCnfigSet(true);
            }
          }
        }
    };

    const requestWhiteboardData = async () => {
        const response = await WhiteboardService.getWhiteboardDetail(
            props.meetingDetails.id,
            room?.localParticipant?.participantInfo
        );
        if (response.success !== 0) {
          const remoteParticipants = Array.from(
              room?.remoteParticipants.values()
          );
          if (
              response.data[0]?.status === "open" &&
              remoteParticipants.length > 0
          ) {
            const encoder = new TextEncoder();
            const data = encoder.encode(
                JSON.stringify({
                  action: DataReceivedEvent.WHITEBOARD_DATA_REQUEST,
                })
            );
            const participant = remoteParticipants[0]?.identity;
            await room?.localParticipant.publishData(data, {
              reliable: true,
              destinationIdentities: [participant],
            });
          }
          setWhiteboardId(() => response.data[0]?.id);
          setIsWhiteboardOpen(response.data[0]?.status === "open");
        }
    };

    if (!props.isHost && !isCoHost) {
      if (props.meetingDetails?.transcription_detail?.transcription_enable) {
        setOpenDrawer(DrawerState.LIVECAPTION);
      }
    }

    const requestLiveCaptionsStateData = async () => {
        const encoder = new TextEncoder();
        const remoteParticipants = Array.from(
            room?.remoteParticipants.values()
        );
        const participant = remoteParticipants.find((part) => {
          try {
            const metadata = part?.metadata ? parseMetadata(part.metadata) : null;
            return metadata?.role_name === "participant";
          } catch (error) {
            setToastNotification(error.message);
            setToastStatus("error");
            setShowToast(true);
            // console.error("Invalid metadata JSON:", part?.metadata, error);
            return false;
          }
        });
        if (participant) {
          const data = encoder.encode(
              JSON.stringify({
                action: DataReceivedEvent.REQUEST_LIVECAPTION_DRAWER_STATE,
              })
          );
          await room?.localParticipant.publishData(data, {
            reliable: true,
            destinationIdentities: [participant?.identity],
          });
        }
    };

    const checkForAutoRecording = async () => {
      if (props.meetingDetails?.meeting_config?.auto_start_recording === 1 &&
          props.meetingDetails?.meeting_config?.recording_force_stopped === 0 &&
          !props.meetingDetails?.is_recording_active &&
          (parseMetadata(room?.localParticipant?.metadata)?.role_name ===
              "moderator" ||
              parseMetadata(room?.localParticipant?.metadata)?.role_name === "cohost")
      ) {
        await new Promise((resolve) => setTimeout(resolve, 6000)); // added delay for 6sec untill room setup
        const response = await SettingsMenuServices.startCloudRecording(
            props.id,
            coHostToken,
            room?.localParticipant?.participantInfo
        );
        if (response.success === 1) setShowRecording(true);
        else setShowRecording(false);
      }
    };

    const checkForCohost = async () => {
      try {
        const cohostDetail = getLocalStorage(constants.CO_HOST_TOKEN);
        if (
            cohostDetail &&
            cohostDetail.token &&
            cohostDetail.meetingId === props.id &&
            parseMetadata(room?.localParticipant?.metadata)?.role_name === "cohost"
        ) {
          setCoHostToken(coHostToken?.token);
          setIsCoHost(true);
        }
      } catch (error) {
        setToastNotification(error.message);
        setToastStatus("error");
        setShowToast(true);
        // console.log("Error: ",error)
      }
    };

    const checkRecordingIsActive = () => {
      if (props.meetingDetails?.is_recording_active) {
        new Audio(recordingStart).play();
        setToastNotification("This meeting is being recorded...");
        setToastStatus("info");
        setShowToast(true);
        setShowRecording(true);
      }
    }

    const checkSpeakerDevice = async () =>{
      if(speakerDeviceId.trim() !== ""){
        try {
          const success = await room.switchActiveDevice('audiooutput', speakerDeviceId);
          if (success) {
            console.log('Successfully switched speaker device.');
          } else {
            console.warn('Failed to switch speaker device. Browser may not support it.');
          }
        } catch (error) {
          console.error('Error switching speaker device:', error);
        }
      }
    }

    checkRecordingIsActive();
    checkForCohost();
    checkForAutoRecording();
    requestLiveCaptionsStateData();
    requestWhiteboardData();
    fetchConfig();
    checkSpeakerDevice();
  }, [room, connected]);

  // Recording status change
  useEffect(() => {
    if (!room) return;
    const onRecordingStatusChange = (isRecording) => {
      // console.log("Recording status changed", isRecording);
      if (isRecording) {
        new Audio(recordingStart).play();
        setToastNotification("Recording started...");
        setToastStatus("info");
        setShowToast(true);
        setShowRecording(true);
        setIsRecordingLoading(false);
      } else {
        new Audio(recordingStop).play();
        setToastNotification("Recording stopped...");
        setToastStatus("info");
        setShowToast(true);
        setShowRecording(false);
        setIsRecordingLoading(false);
      }
    };
    room.on(RoomEvent.RecordingStatusChanged, onRecordingStatusChange);
    return () => {
      room.off(RoomEvent.RecordingStatusChanged, onRecordingStatusChange);
    };
  }, [room]);

  // Timer to hide toast notification after 5 seconds
  useEffect(() => {
    if (!room || !showToast) return;
    const timer = setTimeout(() => {
      if (showToast) setShowToast(false);
    }, 5000);
    return () => clearTimeout(timer);
  }, [room, showToast]);

  // Timer to end breakout room
  useEffect(() => {
    if (!room) return;
    if (!isBreakoutRoom || breakoutRoomEndTime.current === null) return;
    const intervalId = setInterval(async () => {
      const currentTime = moment();
      setEndBreakoutRoomTimer(
          breakoutRoomEndTime.current.diff(currentTime, "seconds")
      );
      if (endBreakoutRoomTimer <= 0) {
        const response = await BreakoutRoomService.getBreakoutRoomToken(
            room.localParticipant,
            `${props.meetingDetails?.room_uid}`
        );
        if (response?.success === 0) {
          console.log("Error getting breakout room token", response);
          return;
        }
        if (response?.data?.access_token) {
          setIsMovingToRoom(true);
          setMovingRoomToken(response?.data?.access_token?.token);
          if (
              parseMetadata(room?.localParticipant?.metadata)?.role_name ===
              "moderator"
          ) {
            await fetchBreakoutRoomDetails();
          }
        }
        breakoutRoomEndTime.current = null;
      }
    }, 1000);
    return () => clearInterval(intervalId);
  }, [room, isBreakoutRoom, endBreakoutRoomTimer, isCoHost]);

  const formatEndBreakoutRoomTime = useCallback((seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, "0")}:${remainingSeconds
        .toString()
        .padStart(2, "0")}`;
  }, []);

  // Alert on back button press
  const [isAlertVisible, setIsAlertVisible] = useState(false);
  
  const handleConfirm = () => {
    setIsAlertVisible(false);
    room.disconnect();
    if (isElectronApp) {
      window?.electronAPI?.ipcRenderer?.send("stop-annotation");
    }
  };

  const handleCancel = () => {
    setIsAlertVisible(false);
    window.history.pushState(null, null, window.location.pathname);
  };

  useEffect(() => {
    const onBackButtonEvent = (e) => {
      e.preventDefault();
      if (!isAlertVisible) {
        e.preventDefault(); // Prevent default back navigation
        setIsAlertVisible(true); // Show alert confirmation
      } else {
        e.preventDefault(); // Prevent default back navigation
        setIsAlertVisible(false); // Hide alert confirmation
      }
    };

    window.addEventListener("popstate", onBackButtonEvent);

    window.history.pushState(null, null, window.location.pathname);
    return () => {
      window.removeEventListener("popstate", onBackButtonEvent);
    };
  }, []);

  const [targetLangChangedFor, setTargetLangChangedFor] = useState(0);
  // Storing the final translated captions
  useEffect(() => {
    // Check if translationDetails?.target_lang is defined and changes
    if (translationDetails?.target_lang && targetLangChangedFor === 0) {
      setTargetLangChangedFor(1);
      setFinalTranslatedCaptions([...finalCaptions]);
    } else if (translationDetails?.target_lang && targetLangChangedFor !== 0) {
      setFinalTranslatedCaptions((prevCaptions) => [...prevCaptions]);
    }
  }, [translationDetails?.target_lang]);

  useEffect(() => {
    if (room) {
      if (focusTrack) {
        setIsFocusTrackEnabled(true);
      } else {
        setIsFocusTrackEnabled(false);
      }
    }
  }, [room, focusTrack]);

  const removeParticipantFromLobby = useCallback((participantId) => {
    const newMap = new Map(lobbyParticipants.current);
    newMap.delete(participantId);
    lobbyParticipants.current = newMap;
  }, []);

  const clearAllLobbyParticipants = useCallback(() => {
    lobbyParticipants.current = new Map();
  }, []);

  // Refs to avoid re-creating the callback on state changes
  const isSelfVideoMirroredRef = useRef(isSelfVideoMirrored);
  const frontCameraMirroringPreferenceRef = useRef(frontCameraMirroringPreference);

  // Cache device enumeration results
  const deviceCacheRef = useRef(new Map());

  // Keep refs in sync with state
  useEffect(() => {
    isSelfVideoMirroredRef.current = isSelfVideoMirrored;
  }, [isSelfVideoMirrored]);

  useEffect(() => {
    frontCameraMirroringPreferenceRef.current = frontCameraMirroringPreference;
  }, [frontCameraMirroringPreference]);

  // Memoized device info fetcher
  const getDeviceInfo = useCallback(async (deviceId) => {
    if (deviceCacheRef.current.has(deviceId)) {
      return deviceCacheRef.current.get(deviceId);
    }
    
    const devices = await navigator.mediaDevices.enumerateDevices();
    const device = devices.find(d => d.kind === "videoinput" && d.deviceId === deviceId);
    
    if (device) {
      const isBackCamera = device.label.toLowerCase().includes('back') ||
                          device.label.toLowerCase().includes('rear') ||
                          device.label.toLowerCase().includes('environment');
      
      const deviceInfo = { device, isBackCamera };
      deviceCacheRef.current.set(deviceId, deviceInfo);
      return deviceInfo;
    }
    
    return null;
  }, []);

  const onActiveDeviceChange = useCallback((_kind, deviceId) => {
    // Set the correct video device ID
    setDeviceIdVideo(deviceId ?? "");

    // Check if the device is a back camera and handle mirroring
    if (deviceId) {
      getDeviceInfo(deviceId).then(deviceInfo => {
        if (deviceInfo) {
          const { device, isBackCamera } = deviceInfo;

          if (isBackCamera) {
            // Store current mirroring preference before switching to back camera
            // (only store if we haven't stored it already)
            if (frontCameraMirroringPreferenceRef.current === undefined) {
              setFrontCameraMirroringPreference(isSelfVideoMirroredRef.current);
            }
            // Force mirroring OFF for back cameras
            setIsSelfVideoMirrored(false);
          } else {
            // Front camera selected
            if (frontCameraMirroringPreferenceRef.current !== undefined) {
              // Restore the user's original front camera preference
              setIsSelfVideoMirrored(frontCameraMirroringPreferenceRef.current);
              // Clear the stored preference since we've restored it
              setFrontCameraMirroringPreference(undefined);
            }
            // If no stored preference, don't change anything (preserve current setting)
          }
        }
      }).catch(error => {
        console.error('Error enumerating devices:', error);
        // Fallback: assume front camera, set mirroring to true
        setIsSelfVideoMirrored(true);
      });
    }
  }, [getDeviceInfo]);

  // Memoize DrawerContainer props to prevent unnecessary recreations
  const drawerContainerProps = useMemo(() => ({
    // Context and drawer state
    openDrawer,
    meetingFeatures,
    
    // Room and participants
    room,
    
    // Props and settings
    id: props.id,
    isHost: props.isHost,
    isCoHost,
    isWhiteboardOpen,
    meetingDetails: props.meetingDetails,
    coHostToken,
    
    // Toast notifications
    setToastNotification,
    setToastStatus,
    setShowToast,
    
    // Chat drawer props
    formatChatMessageLinks,
    privatechatparticipants,
    setPrivateChatParticipants,
    selectedPrivateChatParticipant,
    setSelectedPrivateChatParticipant,
    showPrivateChat,
    setShowPrivateChat,
    privatechatmessages,
    setPrivateChatMessages,
    newMessageRender,
    setPrivateChatUnreadMessagesCount,
    privateChatUnreadMessagesCount,
    setPublicChatUnreadMessagesCount,
    publicChatUnreadMessagesCount,
    canDownloadChatAttachment,
    publicChatMessages,
    setPublicChatMessages,
    privateChatPinnedMessages,
    setPrivateChatPinnedMessages,
    publicChatPinnedMessages,
    setPublicChatPinnedMessages,
    
    // Live captions props
    liveCaptionData,
    liveCaptionsObject,
    setLiveCaptionsObject,
    setFinalCaptions,
    finalCaptions,
    translationDetails,
    setTranslationDetails,
    finalTranslatedCaptions,
    setFinalTranslatedCaptions,
    
    // Participant list props
    layoutContext,
    lobbyParticipants: lobbyParticipants.current,
    setRemoteRaisedHands,
    setShowRaiseHand,
    currentRoomName,
    setBreakoutRoomDuration,
    breakoutRooms,
    setBreakoutRooms,
    setRoomKeyCounter,
    isBreakoutRoomCnfigSet,
    setIsBreakoutRoomCnfigSet,
    forceMute,
    forceVideoOff,
    isBreakoutRoom,
    allowLiveCollabWhiteBoard,
    removeParticipantFromLobby,
    clearAllLobbyParticipants,
    
    // Host control props
    setForceVideoOff,
    setForceMute,
    setCanDownloadChatAttachment,
    canDownloadChatAttachment,
    setAllowLiveCollabWhiteBoard,
    
    // Breakout room props
    roomKeyCounter,
    breakoutRoomDuration,
    
    // Virtual background props
    setCurrentEffect,
    
    // Report problem props
    clientPreferedServerId: props.clientPreferedServerId,
    
    // Recording consent props
    showRecordingConsentDrawer,
    setShowRecordingConsentDrawer,
    participantConsent,
  }), [
    openDrawer,
    meetingFeatures,
    room,
    props.id,
    props.isHost,
    isCoHost,
    isWhiteboardOpen,
    props.meetingDetails,
    coHostToken,
    setToastNotification,
    setToastStatus,
    setShowToast,
    formatChatMessageLinks,
    privatechatparticipants,
    setPrivateChatParticipants,
    selectedPrivateChatParticipant,
    setSelectedPrivateChatParticipant,
    showPrivateChat,
    setShowPrivateChat,
    privatechatmessages,
    setPrivateChatMessages,
    newMessageRender,
    setPrivateChatUnreadMessagesCount,
    privateChatUnreadMessagesCount,
    setPublicChatUnreadMessagesCount,
    publicChatUnreadMessagesCount,
    canDownloadChatAttachment,
    publicChatMessages,
    setPublicChatMessages,
    privateChatPinnedMessages,
    setPrivateChatPinnedMessages,
    publicChatPinnedMessages,
    setPublicChatPinnedMessages,
    liveCaptionData,
    liveCaptionsObject,
    setLiveCaptionsObject,
    setFinalCaptions,
    finalCaptions,
    translationDetails,
    setTranslationDetails,
    finalTranslatedCaptions,
    setFinalTranslatedCaptions,
    layoutContext,
    lobbyParticipants.current,
    setRemoteRaisedHands,
    setShowRaiseHand,
    currentRoomName,
    setBreakoutRoomDuration,
    breakoutRooms,
    setBreakoutRooms,
    setRoomKeyCounter,
    isBreakoutRoomCnfigSet,
    setIsBreakoutRoomCnfigSet,
    forceMute,
    forceVideoOff,
    isBreakoutRoom,
    allowLiveCollabWhiteBoard,
    removeParticipantFromLobby,
    clearAllLobbyParticipants,
    setForceVideoOff,
    setForceMute,
    setCanDownloadChatAttachment,
    canDownloadChatAttachment,
    setAllowLiveCollabWhiteBoard,
    roomKeyCounter,
    breakoutRoomDuration,
    setCurrentEffect,
    props.clientPreferedServerId,
    showRecordingConsentDrawer,
    setShowRecordingConsentDrawer,
    participantConsent,
  ]);

  const renderCount = useRef(0);
  renderCount.current += 1;
  console.log(`RERENDER! Count: ${renderCount.current}`);

  return isPipWindow ? (
    <PipLayout
      layoutContext={layoutContext}
      focusTrack={focusTrack}
      carouselTracks={carouselTracks}
      isForceMuteAll={isForceMuteAll}
      isCoHost={isCoHost}
      isHost={props.isHost}
      isForceVideoOffAll={isForceVideoOffAll}
      isScreenShareEnabled={isScreenShareEnabled}
      onScreenShareChange={onScreenShareChange}
      screenShareMode={screenShareMode}
      maxWidth={props.maxWidth}
      maxHeight={props.maxHeight}
      brightness={brightness}
      participantBrightness={participantBrightness}
    />
  ) : (
    <div
      className={`lk-video-conference ${isElectron() && "lk-video-conference-electron"
        } ${isMobileBrowser() ? "mobile-video-conference" : ""}`}
      {...props}
    >
      {isMobileBrowser() && (
        <div className="mobile-video-conference-upper-controlbar">
          <DaakiaLogo />

          <MediaDeviceMenu
            kind="videoinput"
            onActiveDeviceChange={onActiveDeviceChange}
          >
            <div className="camera-select-icon">
              <VideoSwitch />
            </div>
          </MediaDeviceMenu>


          </div>
        )}
        {isMobileBrowser()
            ? isAlertVisible && (
            <div className="back-alert-mobile">
              <p>Do you want to exit the meeting?</p>
              <div className="back-alert-confirm">
                <Button type="primary" onClick={handleConfirm}>
                  Yes
                </Button>
                <Button onClick={handleCancel}>No</Button>
              </div>
            </div>
        )
            : isAlertVisible && (
            <div className="back-alert-web">
              <div className="back-alert-web-box">
                <p>Do you want to exit the meeting?</p>
                <div className="back-alert-confirm">
                  <Button type="primary" onClick={handleConfirm}>
                    Yes
                  </Button>
                  <Button onClick={handleCancel}>No</Button>
                </div>
              </div>
            </div>
          )}
      {isBreakoutRoom && (
        <div className="br-timer">
          {formatEndBreakoutRoomTime(endBreakoutRoomTimer)}
        </div>
      )}
      {isWeb() && (
        <LayoutContextProvider
          value={layoutContext}
        >
          <div className="lk-video-conference-inner">
            {!isScreenShareEnabled &&
              !isFocusTrackEnabled &&
              !isWhiteboardOpen && (
                <DaakiaLogo
                  className={`daakia-logo ${
                    isElectron() && "daakia-logo-electron"
                  } ${isMobileBrowser() && "hidden"}`}
                />
              )}
            {!focusTrack ? (
              isWhiteboardOpen ? (
                <div className={`lk-focus-layout-wrapper whiteboard-focus ${isMobileBrowser() ? "mobile-whiteboard-focus" : ""}`}>
                  <CarouselLayout tracks={carouselTracks}>
                    <TrackRefContext.Consumer>
                      {(trackRef) => (
                        <ParticipantTile
                          trackRef={trackRef}
                          showEmojiReaction={showEmojiReaction}
                          setShowEmojiReaction={setShowEmojiReaction}
                          showRaiseHand={showRaiseHand}
                          remoteRaisedHands={remoteRaisedHands}
                          remoteEmojiReactions={remoteEmojiReactions}
                          setRemoteEmojiReactions={setRemoteEmojiReactions}
                          brightness={brightness}
                          participantBrightness={participantBrightness}
                        />
                      )}
                    </TrackRefContext.Consumer>
                  </CarouselLayout>
                  <Whiteboard
                    room={room}
                    isWhiteboardOpen={isWhiteboardOpen}
                    setIsWhiteboardOpen={setIsWhiteboardOpen}
                    // socket={socket}
                    token={props.token}
                    isHost={props.isHost}
                    allowLiveCollabWhiteBoard={allowLiveCollabWhiteBoard}
                    isCoHost={isCoHost}
                    whiteboardData={whiteboardData}
                    setWhiteboardData={setWhiteboardData}
                    socket={socket}
                    isSocketConnected={isSocketConnected}
                    isExitWhiteboardModalOpen={isExitWhiteboardModalOpen}
                    setIsExitWhiteboardModalOpen={setIsExitWhiteboardModalOpen}
                    whiteboardSceneData={whiteboardSceneData}
                    setWhiteboardSceneData={setWhiteboardSceneData}
                    meetingId={props.id}
                    whiteBoardId={whiteboardId}
                    meetingDetails={props.meetingDetails}
                    setToastNotification={setToastNotification}
                    setToastStatus={setToastStatus}
                    setShowToast={setShowToast}
                  />
                  <DrawerContainer layoutType="whiteboard" {...drawerContainerProps} />
                </div>
              ) : (
                <div className="lk-grid-layout-wrapper">
                  <GridLayout tracks={tracks}>
                    <TrackRefContext.Consumer>
                      {(trackRef) => (
                        <ParticipantTile
                          trackRef={trackRef}
                          showEmojiReaction={showEmojiReaction}
                          setShowEmojiReaction={setShowEmojiReaction}
                          showRaiseHand={showRaiseHand}
                          remoteRaisedHands={remoteRaisedHands}
                          remoteEmojiReactions={remoteEmojiReactions}
                          setRemoteEmojiReactions={setRemoteEmojiReactions}
                          isSelfVideoMirrored={isSelfVideoMirrored}
                          setIsSelfVideoMirrored={setIsSelfVideoMirrored}
                          brightness={brightness}
                          participantBrightness={participantBrightness}
                        />
                      )}
                    </TrackRefContext.Consumer>
                  </GridLayout>
                  <DrawerContainer layoutType="grid" {...drawerContainerProps} />
                </div>
              )
            ) : (
              <div className={`lk-focus-layout-wrapper ${isMobileBrowser() ? "mobile-focus-layout-wrapper" : ""}`}>
                <FocusLayoutContainer>
                  <CarouselLayout tracks={carouselTracks}>
                    <TrackRefContext.Consumer>
                      {(trackRef) => (
                        <ParticipantTile
                          trackRef={trackRef}
                          showEmojiReaction={showEmojiReaction}
                          setShowEmojiReaction={setShowEmojiReaction}
                          showRaiseHand={showRaiseHand}
                          remoteRaisedHands={remoteRaisedHands}
                          remoteEmojiReactions={remoteEmojiReactions}
                          setRemoteEmojiReactions={setRemoteEmojiReactions}
                          brightness={brightness}
                          participantBrightness={participantBrightness}
                          focusTrack={focusTrack}
                        />
                      )}
                    </TrackRefContext.Consumer>
                  </CarouselLayout>
                  {focusTrack && (
                    <ParticipantTile
                      trackRef={focusTrack}
                      showEmojiReaction={showEmojiReaction}
                      setShowEmojiReaction={setShowEmojiReaction}
                      showRaiseHand={showRaiseHand}
                      remoteRaisedHands={remoteRaisedHands}
                      remoteEmojiReactions={remoteEmojiReactions}
                      setRemoteEmojiReactions={setRemoteEmojiReactions}
                      brightness={brightness}
                      participantBrightness={participantBrightness}
                    />
                  )}
                </FocusLayoutContainer>
                <DrawerContainer layoutType="focus" {...drawerContainerProps} />
              </div>
            )}
            <MeetindEndedModal/>
            <ControlBar
              controls={{
                chat: true,
                settings: !!SettingsComponent,
                info: true,
              }}
              showRaiseHand={showRaiseHand}
              setShowRaiseHand={setShowRaiseHand}
              showEmojiReaction={showEmojiReaction}
              setShowEmojiReaction={setShowEmojiReaction}
              room={room}
              id={props.id}
              connected={connected}
              isHost={props.isHost}
              maxWidth={props.maxWidth}
              maxHeight={props.maxHeight}
              meetingDetails={props.meetingDetails}
              showRecording={showRecording}
              setShowRecording={setShowRecording}
              isCoHost={isCoHost}
              isForceMuteAll={isForceMuteAll}
              isForceVideoOffAll={isForceVideoOffAll}
              coHostToken={coHostToken}
              isBreakoutRoom={isBreakoutRoom}
              remoteParticipants={room.remoteParticipants}
              localParticipant={room.localParticipant}
              meetingFeatures={meetingFeatures}
              privateChatUnreadMessagesCount={privateChatUnreadMessagesCount}
              publicChatUnreadMessagesCount={publicChatUnreadMessagesCount}
              showlivecaptionsicon={liveCaptionsObject.showIcon}
              isWebinarMode={isWebinarMode}
              isWhiteboardOpen={isWhiteboardOpen}
              setIsWhiteboardOpen={setIsWhiteboardOpen}
              screenShareSources={screenShareSources}
              isElectronApp={isElectronApp}
              isExitWhiteboardModalOpen={isExitWhiteboardModalOpen}
              setIsExitWhiteboardModalOpen={setIsExitWhiteboardModalOpen}
              whiteboardSceneData={whiteboardSceneData}
              setWhiteboardSceneData={setWhiteboardSceneData}
              whiteBoardId={whiteboardId}
              setWhiteboardId={setWhiteboardId}
              setScreenShareDisplayId={setScreenShareDisplayId}
              isScreenShareEnabled={isScreenShareEnabled}
              setIsScreenShareEnabled={setIsScreenShareEnabled}
              screenShareMode={screenShareMode}
              setScreenShareMode={setScreenShareMode}
              onScreenShareChange={onScreenShareChange}
              setToastNotification={setToastNotification}
              setToastStatus={setToastStatus}
              setShowToast={setShowToast}
              isSelfVideoMirrored={isSelfVideoMirrored}
              setIsSelfVideoMirrored={setIsSelfVideoMirrored}
              deviceIdAudio={deviceIdAudio}
              setDeviceIdAudio={setDeviceIdAudio}
              isRecordingLoading={isRecordingLoading}
              setIsRecordingLoading={setIsRecordingLoading}
              setParticipantConsent={setParticipantConsent}
              setShowRecordingConsentDrawer={setShowRecordingConsentDrawer}
              showRecordingConsentDrawer={showRecordingConsentDrawer}
              brightness={brightness}
              onBrightnessChange={handleBrightnessChangeWithDataChannel}
              outputVolume={outputVolume}
              onOutputVolumeChange={onOutputVolumeChange}
              autoVideoOff={autoVideoOff}
              onAutoVideoOffChange={onAutoVideoOffChange}
              autoAudioOff={autoAudioOff}
              onAutoAudioOffChange={onAutoAudioOffChange}
              screenShareTracks={screenShareTracks}
              focusTrack={focusTrack}
              speakerDeviceId={speakerDeviceId}
              setSpeakerDeviceId={setSpeakerDeviceId}
            />
          </div>
          <NotificationModal
            notificationVisible={notificationVisible}
            setNotificationVisible={setNotificationVisible}
            content={notificationContent}
            action={notificationAction}
            room={room}
          />
        </LayoutContextProvider>
      )}
      <RoomAudioRenderer />
      {showToast && (

        <StatusNotification
          message={toastNotification}
          status={toastStatus}
          setShow={setShowToast}
        />
      )}
      <ConnectionStateToast state={connectionState} />
    </div>
  );
}